defmodule ExServiceClient.MixProject do
  use Mix.Project

  @version "1.13.0"

  def project do
    [
      app: :ex_service_client,
      version: @version,
      elixir: "~> 1.16",
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      description: description(),
      package: package(),
      # Docs
      name: "Ex Service Client",
      source_url: "https://github.com/stagedates/ex_service_client",
      docs: [
        main: "Ex Service Client",
        #        logo: "",
        extras: ["README.md"]
      ]
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      extra_applications: [:logger],
      mod: {ExServiceClient.Application, []}
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:decorator, "~> 1.4"},
      {:ex_doc, ">= 0.0.0", runtime: false},
      {:hackney, "~> 1.20"},
      {:jason, "~> 1.4"},
      {:joken, "~> 2.5"},
      {:nebulex, "~> 2.6"},
      {:opentelemetry, "~> 1.3"},
      {:opentelemetry_tesla, "~> 2.2.0"},
      {:poison, "~> 6.0"},
      {:tesla, "~> 1.4"},
      {:versioce, "~> 0.2.1"},
      {:styler, "~> 1.4", only: [:dev, :test], runtime: false}
    ]
  end

  defp description do
    "Stagedates service client library."
  end

  defp package do
    [
      # This option is only needed when you don't want to use the OTP application name
      name: "ex_service_client",
      organization: "stagedates",
      # These are the default files included in the package
      files: ~w(lib .formatter.exs mix.exs README*),
      licenses: ["Proprietary"],
      links: %{"GitHub" => "https://github.com/stagedates/ex_service_client"}
    ]
  end
end
