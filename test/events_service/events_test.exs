defmodule EventsService.EventsTest do
  use EventsService.DataCase

  alias EventsService.Events

  describe "events" do
    import EventsService.EventsFixtures

    alias EventsService.Events.Event

    @invalid_attrs %{
      description: nil,
      title: nil,
      category: nil,
      firestore_id: nil,
      is_visible: nil,
      subtitle: nil,
      plan_id: nil,
      thumbnail_url: nil,
      cover_url: nil,
      logo_url: nil,
      show_logo_on_banner: nil,
      start_date: nil,
      end_date: nil,
      venue_id: nil,
      promoter_id: nil,
      balance_account_id: nil,
      admission_date: nil,
      box_office_opening_date: nil,
      kickback: nil,
      published_date: nil,
      deleted_at: nil
    }

    test "list_events/0 returns all events" do
      event = event_fixture()
      assert Events.list_events() == [event]
    end

    test "get_event!/1 returns the event with given id" do
      event = event_fixture()
      assert Events.get_event!(event.id) == event
    end

    test "create_event/1 with valid data creates a event" do
      valid_attrs = %{
        description: "some description",
        title: "some title",
        category: "some category",
        firestore_id: "some firestore_id",
        is_visible: true,
        subtitle: "some subtitle",
        plan_id: "some plan_id",
        thumbnail_url: "some thumbnail_url",
        cover_url: "some cover_url",
        logo_url: "some logo_url",
        show_logo_on_banner: true,
        start_date: ~U[2023-11-12 12:48:00Z],
        end_date: ~U[2023-11-12 12:48:00Z],
        venue_id: "7488a646-e31f-11e4-aace-************",
        promoter_id: "7488a646-e31f-11e4-aace-************",
        balance_account_id: "some balance_account_id",
        admission_date: ~U[2023-11-12 12:48:00Z],
        box_office_opening_date: ~U[2023-11-12 12:48:00Z],
        kickback: 42,
        published_date: ~U[2023-11-12 12:48:00Z],
        deleted_at: ~U[2023-11-12 12:48:00Z]
      }

      assert {:ok, %Event{} = event} = Events.create_event(valid_attrs)
      assert event.description == "some description"
      assert event.title == "some title"
      assert event.category == "some category"
      assert event.firestore_id == "some firestore_id"
      assert event.is_visible == true
      assert event.subtitle == "some subtitle"
      assert event.plan_id == "some plan_id"
      assert event.thumbnail_url == "some thumbnail_url"
      assert event.cover_url == "some cover_url"
      assert event.logo_url == "some logo_url"
      assert event.show_logo_on_banner == true
      assert event.start_date == ~U[2023-11-12 12:48:00Z]
      assert event.end_date == ~U[2023-11-12 12:48:00Z]
      assert event.venue_id == "7488a646-e31f-11e4-aace-************"
      assert event.promoter_id == "7488a646-e31f-11e4-aace-************"
      assert event.balance_account_id == "some balance_account_id"
      assert event.admission_date == ~U[2023-11-12 12:48:00Z]
      assert event.box_office_opening_date == ~U[2023-11-12 12:48:00Z]
      assert event.kickback == 42
      assert event.published_date == ~U[2023-11-12 12:48:00Z]
      assert event.deleted_at == ~U[2023-11-12 12:48:00Z]
    end

    test "create_event/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Events.create_event(@invalid_attrs)
    end

    test "update_event/2 with valid data updates the event" do
      event = event_fixture()

      update_attrs = %{
        description: "some updated description",
        title: "some updated title",
        category: "some updated category",
        firestore_id: "some updated firestore_id",
        is_visible: false,
        subtitle: "some updated subtitle",
        plan_id: "some updated plan_id",
        thumbnail_url: "some updated thumbnail_url",
        cover_url: "some updated cover_url",
        logo_url: "some updated logo_url",
        show_logo_on_banner: false,
        start_date: ~U[2023-11-13 12:48:00Z],
        end_date: ~U[2023-11-13 12:48:00Z],
        venue_id: "7488a646-e31f-11e4-aace-************",
        promoter_id: "7488a646-e31f-11e4-aace-************",
        balance_account_id: "some updated balance_account_id",
        admission_date: ~U[2023-11-13 12:48:00Z],
        box_office_opening_date: ~U[2023-11-13 12:48:00Z],
        kickback: 43,
        published_date: ~U[2023-11-13 12:48:00Z],
        deleted_at: ~U[2023-11-13 12:48:00Z]
      }

      assert {:ok, %Event{} = event} = Events.update_event(event, update_attrs)
      assert event.description == "some updated description"
      assert event.title == "some updated title"
      assert event.category == "some updated category"
      assert event.firestore_id == "some updated firestore_id"
      assert event.is_visible == false
      assert event.subtitle == "some updated subtitle"
      assert event.plan_id == "some updated plan_id"
      assert event.thumbnail_url == "some updated thumbnail_url"
      assert event.cover_url == "some updated cover_url"
      assert event.logo_url == "some updated logo_url"
      assert event.show_logo_on_banner == false
      assert event.start_date == ~U[2023-11-13 12:48:00Z]
      assert event.end_date == ~U[2023-11-13 12:48:00Z]
      assert event.venue_id == "7488a646-e31f-11e4-aace-************"
      assert event.promoter_id == "7488a646-e31f-11e4-aace-************"
      assert event.balance_account_id == "some updated balance_account_id"
      assert event.admission_date == ~U[2023-11-13 12:48:00Z]
      assert event.box_office_opening_date == ~U[2023-11-13 12:48:00Z]
      assert event.kickback == 43
      assert event.published_date == ~U[2023-11-13 12:48:00Z]
      assert event.deleted_at == ~U[2023-11-13 12:48:00Z]
    end

    test "update_event/2 with invalid data returns error changeset" do
      event = event_fixture()
      assert {:error, %Ecto.Changeset{}} = Events.update_event(event, @invalid_attrs)
      assert event == Events.get_event!(event.id)
    end
  end
end
