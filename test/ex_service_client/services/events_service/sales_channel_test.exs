defmodule ExServiceClient.Services.EventsService.SalesChannelTest do
  use ExUnit.Case, async: true

  alias ExServiceClient.Services.EventsService.{SalesChannel, ChannelConfig}

  describe "SalesChannel struct" do
    test "creates a SalesChannel struct with all fields" do
      channel_config = %ChannelConfig{
        id: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        token: "channel_token_123",
        channelKey: "channel_key_123",
        type: "standard",
        value: 10.0,
        label: "Channel Label",
        description: "Channel Configuration Description",
        color: "#FF0000",
        amountOfObjects: 100,
        validUntil: ~U[2024-12-31 23:59:59Z],
        eventId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
      }

      sales_channel = %SalesChannel{
        id: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        originalPrice: 2500,
        quotaMode: "RESERVED",
        variantId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        channelConfig: channel_config,
        channelConfigId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        insertedAt: ~U[2023-01-01 00:00:00Z],
        updatedAt: ~U[2023-01-01 00:00:00Z]
      }

      assert sales_channel.id == "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
      assert sales_channel.originalPrice == 2500
      assert sales_channel.quotaMode == "RESERVED"
      assert sales_channel.variantId == "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
      assert sales_channel.channelConfig == channel_config
      assert sales_channel.channelConfigId == "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
      assert sales_channel.insertedAt == ~U[2023-01-01 00:00:00Z]
      assert sales_channel.updatedAt == ~U[2023-01-01 00:00:00Z]
    end

    test "creates a SalesChannel struct with nil channelConfig" do
      sales_channel = %SalesChannel{
        id: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        originalPrice: 2500,
        quotaMode: "RESERVED",
        variantId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        channelConfig: nil,
        channelConfigId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        insertedAt: ~U[2023-01-01 00:00:00Z],
        updatedAt: ~U[2023-01-01 00:00:00Z]
      }

      assert sales_channel.channelConfig == nil
      assert sales_channel.channelConfigId == "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
    end
  end

  describe "Poison.Decoder" do
    test "decodes datetime fields correctly" do
      sales_channel = %SalesChannel{
        id: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        originalPrice: 2500,
        quotaMode: "RESERVED",
        variantId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        channelConfig: nil,
        channelConfigId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        insertedAt: "2023-01-01T00:00:00Z",
        updatedAt: "2023-01-01T00:00:00Z"
      }

      decoded = Poison.Decoder.decode(sales_channel, [])

      assert decoded.insertedAt == ~U[2023-01-01 00:00:00Z]
      assert decoded.updatedAt == ~U[2023-01-01 00:00:00Z]
    end

    test "handles nil datetime fields" do
      sales_channel = %SalesChannel{
        id: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        originalPrice: 2500,
        quotaMode: "RESERVED",
        variantId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        channelConfig: nil,
        channelConfigId: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        insertedAt: nil,
        updatedAt: nil
      }

      decoded = Poison.Decoder.decode(sales_channel, [])

      assert decoded.insertedAt == nil
      assert decoded.updatedAt == nil
    end
  end
end
