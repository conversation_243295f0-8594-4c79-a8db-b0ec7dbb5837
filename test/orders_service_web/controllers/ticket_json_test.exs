defmodule OrdersServiceWeb.TicketJSONTest do
  use OrdersService.DataCase, async: true

  import OrdersService.Factory

  alias ExServiceClient.Services.EventsService.SalesChannel
  alias OrdersServiceWeb.TicketJSON

  describe "show_details_data/1" do
    test "includes distribution type information for regular tickets" do
      ticket =
        build(:ticket,
          distribution_type: :REGULAR,
          distribution_type_id: nil,
          distribution_type_info: %{type: :REGULAR}
        )

      order_ticket = build(:order_ticket, ticket: ticket)

      result = TicketJSON.show_details_data(order_ticket)

      assert result.distributionType == :REGULAR
      assert result.distributionTypeId == nil
      assert result.distributionTypeInfo == %{type: :REGULAR}
      assert result.id == ticket.id
      assert result.eventId == ticket.event_id
      assert result.variantId == ticket.variant_id
    end

    test "includes distribution type information for sales channel tickets" do
      sales_channel_id = Faker.UUID.v4()

      mock_sales_channel = %SalesChannel{
        id: sales_channel_id,
        originalPrice: 2000,
        quotaMode: "SHARED",
        variantId: Faker.UUID.v4(),
        channelConfigId: Faker.UUID.v4(),
        insertedAt: DateTime.utc_now(),
        updatedAt: DateTime.utc_now(),
        channelConfig: nil
      }

      ticket =
        build(:ticket,
          distribution_type: :SALES_CHANNEL,
          distribution_type_id: sales_channel_id,
          distribution_type_info: %{
            type: :SALES_CHANNEL,
            sales_channel: mock_sales_channel
          }
        )

      order_ticket = build(:order_ticket, ticket: ticket)

      result = TicketJSON.show_details_data(order_ticket)

      assert result.distributionType == :SALES_CHANNEL
      assert result.distributionTypeId == sales_channel_id
      assert result.distributionTypeInfo.type == :SALES_CHANNEL
      assert result.distributionTypeInfo.id == sales_channel_id
      assert result.distributionTypeInfo.originalPrice == 2000
      assert result.distributionTypeInfo.quotaMode == "SHARED"
    end

    test "includes distribution type information for guest list invitation tickets" do
      invitation_id = Faker.UUID.v4()

      ticket =
        build(:ticket,
          distribution_type: :GUEST_LIST_INVITATION,
          distribution_type_id: invitation_id,
          distribution_type_info: %{type: :GUEST_LIST_INVITATION}
        )

      order_ticket = build(:order_ticket, ticket: ticket)

      result = TicketJSON.show_details_data(order_ticket)

      assert result.distributionType == :GUEST_LIST_INVITATION
      assert result.distributionTypeId == invitation_id
      assert result.distributionTypeInfo == %{type: :GUEST_LIST_INVITATION}
    end

    test "includes all expected ticket fields" do
      ticket =
        build(:ticket,
          distribution_type: :REGULAR,
          distribution_type_info: %{type: :REGULAR},
          attendee: build(:personal_information),
          owner: build(:personal_information)
        )

      order_ticket =
        build(:order_ticket,
          ticket: ticket,
          bill: build(:bill)
        )

      result = TicketJSON.show_details_data(order_ticket)

      # Verify all expected fields are present
      expected_fields = [
        :admission,
        :attendee,
        :bill,
        :categoryId,
        :categoryName,
        :checkInDate,
        :distributionType,
        :distributionTypeId,
        :distributionTypeInfo,
        :eventId,
        :id,
        :multiLevelPricingModifierLabel,
        :owner,
        :purchaseDate,
        :refundTransactions,
        :scanCodes,
        :seat,
        :status,
        :type,
        :variantId
      ]

      Enum.each(expected_fields, fn field ->
        assert Map.has_key?(result, field), "Missing field: #{field}"
      end)
    end
  end

  describe "show_details_index/1" do
    test "processes multiple order tickets correctly" do
      tickets = [
        build(:ticket, distribution_type: :REGULAR, distribution_type_info: %{type: :REGULAR}),
        build(:ticket,
          distribution_type: :GUEST_LIST_INVITATION,
          distribution_type_info: %{type: :GUEST_LIST_INVITATION}
        )
      ]

      order_tickets = Enum.map(tickets, &build(:order_ticket, ticket: &1))

      result = TicketJSON.show_details_index(%{order_tickets: order_tickets})

      assert is_list(result)
      assert length(result) == 2

      [first_ticket, second_ticket] = result
      assert first_ticket.distributionType == :REGULAR
      assert second_ticket.distributionType == :GUEST_LIST_INVITATION
    end

    test "handles empty order tickets list" do
      result = TicketJSON.show_details_index(%{order_tickets: []})

      assert result == []
    end
  end

  describe "struct serialization" do
    test "properly serializes SalesChannel struct to JSON-compatible map" do
      sales_channel_id = Faker.UUID.v4()

      channel_config = %ExServiceClient.Services.EventsService.ChannelConfig{
        id: Faker.UUID.v4(),
        token: "test-token",
        channelKey: "test-key",
        type: "standard",
        value: 10.0,
        label: "Test Channel",
        description: "Test Description",
        color: "#FF0000",
        amountOfObjects: 100,
        validUntil: ~U[2024-12-31 23:59:59Z],
        eventId: Faker.UUID.v4()
      }

      sales_channel = %SalesChannel{
        id: sales_channel_id,
        originalPrice: 2500,
        quotaMode: "RESERVED",
        variantId: Faker.UUID.v4(),
        channelConfigId: channel_config.id,
        insertedAt: ~U[2023-01-01 00:00:00Z],
        updatedAt: ~U[2023-01-01 00:00:00Z],
        channelConfig: channel_config
      }

      ticket =
        build(:ticket,
          distribution_type: :SALES_CHANNEL,
          distribution_type_id: sales_channel_id,
          distribution_type_info: %{
            type: :SALES_CHANNEL,
            sales_channel: sales_channel
          }
        )

      order_ticket = build(:order_ticket, ticket: ticket)

      result = TicketJSON.show_details_data(order_ticket)

      # Verify the struct was properly serialized to a map (flattened structure)
      assert is_map(result.distributionTypeInfo)
      assert result.distributionTypeInfo.type == :SALES_CHANNEL
      assert result.distributionTypeInfo.id == sales_channel_id
      assert result.distributionTypeInfo.originalPrice == 2500
      assert result.distributionTypeInfo.quotaMode == "RESERVED"

      # Verify channel config was also serialized
      assert is_map(result.distributionTypeInfo.channelConfig)
      assert result.distributionTypeInfo.channelConfig.token == "test-token"
      assert result.distributionTypeInfo.channelConfig.label == "Test Channel"
    end
  end
end
