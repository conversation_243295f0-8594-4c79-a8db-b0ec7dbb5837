# Changelog

All notable changes to this project will be documented in this file.

## [4.44.2] - 2025-08-18

### Bug Fixes

- Filter all ended (not only closed) events (#1012)

## [4.44.1] - 2025-08-15

### Bug Fixes

- Update ex_rbac to fix permission check (#1008)
- Add event.id to the event sorting to make it unique (#1010)
- Adding nil check for transform_event_permission on created_by (#1009)

### Miscellaneous Tasks

- *(release)* V4.44.1 (#1011)

## [4.44.0] - 2025-08-14

### Features

- Implement seller migrator for creating missing sellers and perm… (#994)
- Reduce max length for a slug and a venue name (#998)

### Miscellaneous Tasks

- Remove firebase shortlink creation (#1006)
- *(release)* V4.44.0 (#1007)

## [4.43.0] - 2025-08-13

### Features

- Enhance organizer access check with seller permissions and back… (#1001)

### Bug Fixes

- Make the clients working again (#1004)

### Miscellaneous Tasks

- *(release)* V4.43.0 (#1005)

## [4.42.2] - 2025-08-13

### Bug Fixes

- Use correct sorting for promoter events overview (#1002)

### Miscellaneous Tasks

- *(release)* V4.42.2 (#1003)

## [4.42.1] - 2025-08-12

### Bug Fixes

- Allow no availability for sales channel update (#999)

### Miscellaneous Tasks

- *(release)* V4.42.1 (#1000)

## [4.42.0] - 2025-08-11

### Features

- Seller permission owner on promoter create (#993)
- Finalize events five days after the event ends (#996)

### Miscellaneous Tasks

- *(release)* V4.42.0 (#997)

## [4.41.1] - 2025-08-07

### Bug Fixes

- Add x-Seller-Id header to CORS configuration (#990)

### Miscellaneous Tasks

- *(release)* V4.41.1 (#992)

## [4.41.0] - 2025-08-07

### Features

- Change tracking link URL (#987)
- Use shortlink service for tracking (#988)

### Miscellaneous Tasks

- Update ex_service_client (#989)
- *(release)* V4.41.0 (#991)

## [4.40.0] - 2025-08-04

### Features

- Adding voucher.code to internal events API and to_internal_event_details_dto (#985)

### Miscellaneous Tasks

- Update deps (#984)
- *(release)* V4.40.0 (#986)

## [4.39.0] - 2025-08-04

### Features

- Integrate ShortlinkService for link creation (#975)
- Add some critical loggings to event CRUD operation (#979)

### Bug Fixes

- Adjust channel config changes for SalesChannel with sold tickets (#978)
- Use correct naming (#982)

### Refactor

- Adjust internal API call (#980)

## [4.38.0] - 2025-07-29

### Features

- *(event index)* Add event index API with description (#976)

### Miscellaneous Tasks

- *(release)* V4.38.0 (#977)

## [4.37.1] - 2025-07-29

### Bug Fixes

- Revert "fix(events): available tickets calculation (#877)" (#969)
- Properly parse scope string to atom for permission validation (#970)
- Search only regular tickets for cheapest gross price (#971)

### Refactor

- Add VariantQuota module for calculating variant quotas and … (#972)

### Miscellaneous Tasks

- *(release)* V4.37.1 (#974)

## [4.37.0] - 2025-07-23

### Features

- Add migrator for user ids (#962)
- *(events)* Allow MLP operations on ticket categories with existing items for draft events (#965)
- *(events)* Support created_by field on event and transform event p… (#684)

### Bug Fixes

- Use user_id if possible for entrance areas (#675)
- Add promoter user migration (#966)

### Miscellaneous Tasks

- Formatting (#967)
- *(release)* V4.37.0 (#968)

## [4.36.0] - 2025-07-21

### Features

- Add seller context and permission check (#937)

### Bug Fixes

- Change promoter events quota calculation check to variant.distri… (#959)
- Promoter event api, filter draft out of completed and upcoming status filter (#960)
- Refactoring promoter event params to properly set default values (#961)
- Add voucher_counter preload to list_vouchers (#963)

### Miscellaneous Tasks

- *(release)* V4.36.0 (#964)

## [4.35.0] - 2025-07-15

### Features

- *(seller)* Add seller permissions model, migration and prop... (#924)
- *(seller_permissions)* Crud apis (#925)
- *(seller)* Users seller index api (#934)
- Make display name for an organizer required on the db (#957)

### Bug Fixes

- Validate voucher code (#956)

### Miscellaneous Tasks

- Formatting (#955)
- *(release)* V4.35.0 (#958)

## [4.34.1] - 2025-07-10

### Bug Fixes

- Preload availabililty and ticket category on event details page

### Miscellaneous Tasks

- *(release)* V4.34.1 (#954)

## [4.34.0] - 2025-07-10

### Features

- Make display name mandatory for promoter entity (#952)
- Add quotas to promoter events reponse (#951)

### Miscellaneous Tasks

- *(release)* V4.34.0 (#953)

## [4.33.3] - 2025-07-10

### Bug Fixes

- Fetch unique constraint error for vouchers (#943)
- Round event fees (to cents) (#949)

### Refactor

- Imporve performance for event APIs (#928)

### Styling

- Remove unused code (#944)

### Miscellaneous Tasks

- *(release)* V4.33.3 (#950)

## [4.33.2] - 2025-07-09

### Bug Fixes

- Remove columns again (#947)

### Miscellaneous Tasks

- *(release)* V4.33.2 (#948)

## [4.33.1] - 2025-07-09

### Refactor

- Remove unused attributes from event counter (#945)

### Miscellaneous Tasks

- *(release)* V4.33.1 (#946)

## [4.33.0] - 2025-07-08

### Features

- *(draft)* Modify draft event validations (#915)

### Bug Fixes

- Use correct pattern match for organizer validation
- Remove-duplicated-function (#939)
- Use RESTful vouchers APIs (#940)
- Don't make closed_at required (#941)

### Miscellaneous Tasks

- *(release)* V4.33.0 (#942)

## [4.32.1] - 2025-07-04

### Bug Fixes

- Use user_document_id to validate organizer (#936)

### Miscellaneous Tasks

- *(release)* V4.32.1 (#938)

## [4.32.0] - 2025-07-03

### Features

- Remove unused voucher APIs (#929)
- Validate ticket category quota (#931)
- *(sellers)* Add support for ORGANIZER seller type (#908)

### Bug Fixes

- Don't preload ticket categeory counter for new ticket categories (#933)

### Documentation

- Add missing attributes to voucher API documentation (#932)

### Miscellaneous Tasks

- Remove dead code (#930)
- Run mix format
- Remove compiler warning
- *(release)* V4.32.0 (#935)

## [4.31.0] - 2025-06-30

### Features

- Exten variants api by type filter | refactor (#922)
- Add validFrom and validUntil to Vouchers (#920)
- *(observability)* Enable OTLP tracing in all environments (#926)

### Bug Fixes

- Properly filter seats and admission flags in variants API (#923)

### Miscellaneous Tasks

- *(release)* V4.31.0 (#927)

## [4.30.0] - 2025-06-27

### Features

- Adding venue info to promoter events API (#912)
- Add group_by param to guestlist invitation statistics (#909)
- Reworking is_default handling in variants API | extending filte… (#916)

### Bug Fixes

- Sync event for mlpm, sales channel and ticket category (#897)

### Miscellaneous Tasks

- Create GitHub PR template (#917)
- *(release)* V4.30.0 (#918)

## [4.29.0] - 2025-06-18

### Features

- *(script)* Remove execute_oban_jobs action (#906)

### Bug Fixes

- Add gke_endpoint env to runtime.exs (#905)

### Documentation

- Adjust variants API documentation (#902)

### Miscellaneous Tasks

- *(release)* V4.29.0 (#907)

## [4.28.5] - 2025-06-17

### Bug Fixes

- *(copy)* Create event permission for EA security user (#903)

### Miscellaneous Tasks

- *(release)* V4.28.5 (#904)

## [4.28.4] - 2025-06-17

### Bug Fixes

- Add security user generation to event copy (#900)

### Miscellaneous Tasks

- *(release)* V4.28.4 (#901)

## [4.28.3] - 2025-06-17

### Bug Fixes

- *(invitation)* Fallback for admins (#894)

### Miscellaneous Tasks

- Unleash flag snake_case (#898)
- *(release)* V4.28.3 (#899)

## [4.28.2] - 2025-06-17

### Bug Fixes

- *(invitation)* Fallback to user name (#892)

### Miscellaneous Tasks

- Add userId t o copy_event feauture toggle (#895)
- *(release)* V4.28.2 (#896)

## [4.28.1] - 2025-06-17

### Bug Fixes

- *(entrance_area)* Remove check for used tickets on delete (#889)

### Miscellaneous Tasks

- *(release)* V4.28.1 (#893)

## [4.28.0] - 2025-06-16

### Features

- *(sales-permissions)* Add event permissions for organizer permissions (#878)
- Adding asset GET API to fetch event summary pdf (#886)
- Do not check origin for websocket connection (#888)

### Bug Fixes

- Use proper websocket route (#887)

### Miscellaneous Tasks

- *(release)* V4.28.0 (#890)

## [4.27.0] - 2025-06-05

### Features

- *(copy)* Add optional 'copy_options' (#884)

### Miscellaneous Tasks

- *(release)* V4.27.0 (#885)

## [4.26.1] - 2025-06-03

### Bug Fixes

- Update libs (#881)

### Miscellaneous Tasks

- *(release)* V4.26.1 (#882)

## [4.26.0] - 2025-06-03

### Features

- Add copy events functionality (#858)
- *(copy)* Do not copyy availabilities (#879)

### Bug Fixes

- *(events)* Available tickets calculation (#877)

### Miscellaneous Tasks

- *(release)* V4.26.0 (#880)

## [4.25.0] - 2025-05-28

### Features

- *(DB)* Add db dump to set up a new database more easily (#872)

### Refactor

- Clean up update variant (#875)

### Miscellaneous Tasks

- *(release)* V4.25.0 (#876)

## [4.24.0] - 2025-05-23

### Features

- Remove experimental flag from events and cities APIs (#873)

### Miscellaneous Tasks

- *(release)* V4.24.0 (#874)

## [4.23.0] - 2025-05-22

### Features

- *(variant)* Include mlpm in service call response (#869)
- Adding gzip compression to configs (#870)

### Miscellaneous Tasks

- *(release)* V4.23.0 (#871)

## [4.22.0] - 2025-05-21

### Features

- *(minAmount)* Stop sale if quto is behind (min) minAmount (#862)
- *(events)* Improve event sorting by adding support for snake_case sort params (#866)

### Bug Fixes

- Add MLPM to preload before check whether sales has to be stopped (#863)
- Preload MLPM in ticket category counter handler (#864)
- Get promoter from event (#867)
- *(seller)* Cast store name (#865)

### Miscellaneous Tasks

- *(release)* V4.22.0 (#868)

## [4.21.0] - 2025-05-19

### Features

- Don't delete SalesChannels on publish an event (#860)

### Miscellaneous Tasks

- *(release)* V4.21.0 (#861)

## [4.20.0] - 2025-05-16

### Features

- Delete sales permissions (#850)
- *(seller)* Add store name field (#849)

### Bug Fixes

- Soft delete sales permisssion (#855)
- Use correct validation for used Tickets (#857)

### Miscellaneous Tasks

- *(release)* V4.20.0 (#856)

## [4.19.0] - 2025-05-14

### Features

- Adding service get_seller_by_id API (#848)
- *(sales-permissions)* Add pagination to sales permissions endpoint (#851)
- *(voucher)* Add min items to voucher (#852)

### Bug Fixes

- Make min_items mandatory for vouchers (#853)

### Miscellaneous Tasks

- *(release)* V4.19.0 (#854)

## [4.18.3] - 2025-05-12

### Bug Fixes

- *(events)* Calculate available tickets using public variants (#844)
- *(sales_channels)* Use ticket category quota for reporting (#846)

### Miscellaneous Tasks

- *(release)* V4.18.3 (#847)

## [4.18.2] - 2025-05-09

### Bug Fixes

- Use correct sold values for the ticket category

### Miscellaneous Tasks

- *(release)* V4.18.2 (#845)

## [4.18.1] - 2025-05-09

### Bug Fixes

- Add nil check to counter update (#842)

### Miscellaneous Tasks

- *(release)* V4.18.1 (#843)

## [4.18.0] - 2025-05-09

### Features

- Add ticket category to counter sync (#840)

### Miscellaneous Tasks

- *(release)* V4.18.0 (#841)

## [4.17.1] - 2025-05-08

### Bug Fixes

- Adding nil check for sales_channel.variant in sales_channel_json.ex (#832)
- Adjust event permission check (#835)
- Preload sales channel for variants (#837)
- Adjust event permission check to delete vouchers (#838)

### Miscellaneous Tasks

- *(release)* V4.17.1 (#839)

## [4.17.0] - 2025-05-08

### Features

- Adjust voucher APIs (#830)

### Bug Fixes

- Correct voucher naming in delete_all_multi in event publish (#833)

### Miscellaneous Tasks

- *(release)* V4.17.0 (#834)

## [4.16.0] - 2025-05-08

### Features

- *(cache)* Reduce cache max age to 60 seconds (#814)
- *(seller-events)* Add venue city filter for seller events (#804)
- *(seller)* Add seller permission check logic (#809)
- Add logging to cities endpoint (#820)
- *(event_controller)* Use user_payload for affiliation (#823)
- *(voucher)* Scope vouchers (#819)
- Change MLP for a TicketCategory (#824)
- Adding insertedAt to variant response for organizer data (#829)

### Bug Fixes

- Bump styler (#825)
- Remove unique index for voucher codes (#828)

### Other

- Add logger (#826)

### Refactor

- *(seller)* Refactor seller ID extraction from user payload (#827)

### Miscellaneous Tasks

- Upate version
- *(release)* V4.16.0 (#831)

## [4.15.1] - 2025-05-02

### Bug Fixes

- Donot filter events that started 12 hours ago (#817)

### Miscellaneous Tasks

- *(release)* V4.15.1 (#818)

## [4.15.0] - 2025-04-30

### Features

- *(event)* Add venue to event response (#810)
- *(mlp)* Update base price for ticket categories with MLP (#802)

### Miscellaneous Tasks

- *(release)* V4.15.0 (#816)

## [4.14.0] - 2025-04-29

### Features

- Create and view sales permissions (#796)
- Add sorting to ticket category API (#808)
- Add SoftDelete Repo (#813)

### Bug Fixes

- Use use nil for min and max amount (#806)
- Don't use nil for existingItems (#807)
- Use correct unique index for default modifier (#811)
- Use correct defparams (#812)
- Use correct order syntax

### Miscellaneous Tasks

- *(release)* V4.14.0 (#815)

## [4.13.0] - 2025-04-26

### Features

- *(mlp)* Add existingItems to MLP API (#800)
- Make regular sold a mandatory response (#803)

### Bug Fixes

- Use camelCase for API payload and response (#801)
- Add mlpId to variants API

### Miscellaneous Tasks

- *(release)* V4.13.0 (#805)

## [4.12.0] - 2025-04-24

### Features

- Unassign a MLP from a ticket category (#793)
- Use seperate deployment for dev environment

### Bug Fixes

- Delete modifier from default variant (#798)

### Miscellaneous Tasks

- *(release)* V4.12.0 (#799)

## [4.11.0] - 2025-04-23

### Features

- Allow negative price factor for MLPM (#795)
- Add orderNo to MLPMs (#794)

### Miscellaneous Tasks

- *(release)* V4.11.0 (#797)

## [4.10.0] - 2025-04-22

### Features

- *(seller)* Create new seller (#782)
- *(seatsio)* Change hold token expiration error response to 404 Not Found (#787)
- Add MLPM to variants API (for organizer view) (#789)

### Bug Fixes

- *(events)* Handle fee amounts as strings or floats (#788)
- Remove unused and broken modifier API (#791)

### Refactor

- Remove double preload (#790)

### Miscellaneous Tasks

- *(release)* V4.10.0 (#792)

## [4.9.0] - 2025-04-16

### Features

- *(schema)* Add sales permissions, sellers, and stores (#781)

### Bug Fixes

- *(variant)* Status calculation (#784)
- *(variant)* Status calculation for tickets with predecessors (#785)

### Miscellaneous Tasks

- *(release)* V4.9.0 (#786)

## [4.8.1] - 2025-04-14

### Bug Fixes

- Adjust promoter events filter for upcoming to closed_at (#779)

### Miscellaneous Tasks

- *(release)* V4.8.1 (#780)

## [4.8.0] - 2025-04-14

### Features

- *(deps)* Upgrade ex_rbac to 1.4.1 (#777)

### Bug Fixes

- Use ticket category quota for entrance area overview (#775)

### Refactor

- Clean up MLPM API (#773)
- Clean up sales channel API (#776)

### Miscellaneous Tasks

- *(release)* V4.8.0 (#778)

## [4.7.0] - 2025-04-11

### Features

- *(cache_headers)* Handle nil response body for file downloads (#772)

### Miscellaneous Tasks

- *(release)* V4.7.0 (#774)

## [4.6.0] - 2025-04-10

### Features

- Update assigned mlp (#769)

### Miscellaneous Tasks

- *(release)* V4.6.0 (#771)

## [4.5.0] - 2025-04-10

### Features

- *(router)* Add cache headers plug to events routes (#767)
- *(cache)* Add cache headers and ETag handling (#768)

### Miscellaneous Tasks

- *(release)* V4.5.0 (#770)

## [4.4.0] - 2025-04-08

### Features

- *(seatsio)* Change error status to bad_request for hold token failure (#765)

### Miscellaneous Tasks

- *(release)* V4.4.0 (#766)

## [4.3.0] - 2025-04-08

### Features

- Add not null contraint to ticket_categories.price (#760)
- Do not allow organizers to switch legal entity type during (#762)

### Bug Fixes

- Disallow variant deletion for Seats.io tickets (#763)

### Refactor

- Remove migration code (#761)

### Miscellaneous Tasks

- *(release)* V4.3.0 (#764)

## [4.2.3] - 2025-04-07

### Bug Fixes

- *(events)* Round gross price to avoid decimal places (#758)

### Miscellaneous Tasks

- *(release)* V4.2.3 (#759)

## [4.2.2] - 2025-04-04

### Bug Fixes

- Use correct contingent for event overview (#755)

### Miscellaneous Tasks

- *(release)* V4.2.2 (#757)

## [4.2.1] - 2025-04-04

### Bug Fixes

- Handle flat modifier nil value (#753)

### Miscellaneous Tasks

- *(release)* V4.2.1 (#754)

## [4.2.0] - 2025-04-03

### Features

- *(fees)* Add apply_to property to PlatformFees (#751)

### Bug Fixes

- *(ticket_category)* Error handling for missing default modifier (#746)

### Miscellaneous Tasks

- *(release)* V4.2.0 (#752)

## [4.1.0] - 2025-04-03

### Features

- *(mlpm)* Add order_no column to mlpm table (#748)
- *(controllers)* Replace VerifyAccess plug (#747)
- *(event_permission_validator)* Check permissions for created_by user (#690)

### Bug Fixes

- Rename `AssetControllerJSON` to `AssetJSON` (#749)

### Miscellaneous Tasks

- *(release)* V4.1.0 (#750)

## [4.0.0] - 2025-04-02

### Features

- [**breaking**] Move contingent management to ticket category (#681)

### Miscellaneous Tasks

- *(release)* V4.0.0 (#745)

## [3.7.0] - 2025-04-02

### Features

- *(mlp)* Return full dto for organizer in variants api (#709)
- *(variant_json)* Expose public MLPM data (#710)
- *(fee)* Add flat and percentage modifiers to fee (#714)
- Add price to TicketCategoryOrganizerDTO (#729)

### Bug Fixes

- Use 1xn relationship between MLPM and variant (#705)
- Enforce by policies requires enforcing by enforcer (#703)
- List only regular variants in public index API (#713)
- Enhance event summary mail example ticket to display price level, entrance area and default barcode (#715)
- *(mlp)* Adjust ticket category route (#722)
- Move } and don't do silly mistakes ;) (#723)
- Check variant during update process (#725)
- Add price to TicketCategory update attrs (#726)
- Update user claims with add and remove keys (#727)
- *(mlp)* Fix de/en locale for Money struct in flat_modifier (#728)
- Hide non default variant for organzier #730)
- Don't allow price updates for variants with MLPM (#731)
- Don't allow to update a used MLP or MLPM (#732)
- Update all variant siblings (#733)
- Don't allow Extras to have a MLP (#734)
- Use correct MLP pattern match (#735)
- Add min and max amount to default variants (#736)
- Add predecessor_id to sibling variants (#739)
- Remove typos (#738)

### Other

- Add MLP to TicketCategory (#717)

### Miscellaneous Tasks

- *(release)* V3.7.0 (#744)

## [3.6.0] - 2025-03-26

### Features

- Expand public variant API with MLPM (#700)
- Expand organizer variant APIs (#702)

### Miscellaneous Tasks

- *(release)* V3.6.0 (#706)

## [3.5.1] - 2025-03-26

### Bug Fixes

- Show all upcoming tickets (#698)

### Miscellaneous Tasks

- *(release)* V3.5.1 (#701)

## [3.5.0] - 2025-03-25

### Features

- Sync contingent management to orders-service (#677)
- *(mlp)* Add optional notes field to MultiLevelPricing (#682)

### Bug Fixes

- Adding @derive Jason.Encoder to mail.ex (#685)
- Remove typo (#686)
- Remove deprecated ticket_category from event_creation_mail.ex (#687)
- Use proper venue properties in put_venue_short (#692)
- Using dto to publish event to events.events (#694)

### Miscellaneous Tasks

- *(release)* V3.5.0 (#697)

## [3.4.0] - 2025-03-21

### Features

- Use mlpm dto for mlpms within mlp (#678)
- *(event)* Add `createdByDocumentId` field to event JSON (#676)

### Miscellaneous Tasks

- *(release)* V3.4.0 (#679)

## [3.3.1] - 2025-03-20

### Bug Fixes

- Preload channel_config to variants (#673)
- Add base_data to SalesChannelJSON

### Miscellaneous Tasks

- *(release)* V3.3.1 (#674)

## [3.3.0] - 2025-03-20

### Features

- Add kubectl current-context to mirrord script (#668)

### Bug Fixes

- Change PubSub configuration (#669)

### Refactor

- Rename PUSUB_EMULATOR_URL to PUBSUB_BASE_URL (#671)

### Miscellaneous Tasks

- *(release)* V3.3.0 (#672)

## [3.2.1] - 2025-03-18

### Refactor

- *(pubsub)* Clean up code (#666)

### Miscellaneous Tasks

- *(release)* V3.2.1 (#667)

## [3.2.0] - 2025-03-18

### Features

- *(event_permission)* User id support (#653)
- *(pubsub)* Use PubSub emulator in minikube (#661)

### Bug Fixes

- Change .get_env to events_service instead orders (#664)

### Miscellaneous Tasks

- *(release)* V3.2.0 (#665)

## [3.1.0] - 2025-03-17

### Features

- Debug commit to trigger git cliff (#662)

### Miscellaneous Tasks

- *(release)* V3.1.0 (#663)

## [3.0.2] - 2025-03-14

### Bug Fixes

- Don't use default PK for TicketCategoryCounter (#657)
- Use correct validation (#658)

### Miscellaneous Tasks

- *(release)* V3.0.2 (#659)

## [3.0.1] - 2025-03-13

### Bug Fixes

- Disable ticket counter handler to avoid a error during checkout (#655)

### Miscellaneous Tasks

- *(release)* V3.0.1 (#656)

## [3.0.0] - 2025-03-13

### Features

- Add DB trigger to channel_configs table(#651)
- *(router)* [**breaking**] Remove deprecated APIs (#652)

### Miscellaneous Tasks

- *(release)* V3.0.0 (#654)

## [2.11.0] - 2025-03-13

### Features

- *(permission)* Add permissions via casdoor (#647)
- Add quota to ticket category (#648)

### Bug Fixes

- Update rbac dependency (#649)

### Miscellaneous Tasks

- *(release)* V2.11.0 (#650)

## [2.10.0] - 2025-03-11

### Features

- *(countries)* Add flagEmoji
- *(countries)* Add flagEmoji (#644)
- *(country)* Add single country API

### Bug Fixes

- Adding event update error handling for patch on completed events (#637)

### Miscellaneous Tasks

- Clean up dev.exs
- Clean up prod.exs
- Add BACKEND_URL to .env.template
- Run mix format
- Run mix format
- Update mix.lock
- Styler sort
- Styler sort country schema
- Make some PR changes
- *(release)* V2.10.0 (#646)

## [2.9.0] - 2025-03-10

### Features

- Deactivate deprecated APIs (#623)
- Add VAT ID to vendor profile (#639)

### Miscellaneous Tasks

- *(release)* V2.9.0 (#641)

## [2.8.0] - 2025-03-10

### Features

- Use BACKEND_URL ENV to configure service client (#628)

### Miscellaneous Tasks

- *(release)* V2.8.0 (#638)

## [2.7.0] - 2025-03-10

### Features

- Add gar image build to events service (#619)
- Add makefile and GitHub Actions workflow for release management (#622)
- Convert sorting params to camel case (#625)
- Add attendees sort params (#627)

### Bug Fixes

- Add missing path for tracking link response parsing (#617)
- Events-Service: Promoter API should return correct error status (#626)
- Missing event counters on sort (#629)
- Put promoter into event when creating new workspace (#631)
- Seatsio-put-workspacekeys-into-promoter (#632)
- *(seatsio)* Use correct key at publish event when copy chart (#633)
- *(seatsio)* Add private workspace key to retrieve chart (#634)

### Miscellaneous Tasks

- Cleanup build and remove depr gcr image (#621)
- Finish git release setup (#635)
- *(release)* V2.7.0 (#636)

## [2.6.2] - 2025-02-25

### Bug Fixes

- Udpate service_client to use retries (#616)

### Miscellaneous Tasks

- Update patch version (#618)

## [2.6.1] - 2025-02-25

### Bug Fixes

- Adding _event_counter to filter_venue_city query (#613)

### Miscellaneous Tasks

- Bump version to 2.6.1 (#614)

## [2.6.0] - 2025-02-24

### Features

- Promoter store (#589)

### Bug Fixes

- Wrong join breaking `get_edit_event` (#606)
- *(db)* Remove double index (#607)
- Filter invalid address out (#610)

### Miscellaneous Tasks

- Bump to version 2.5.5 (#611)

## [2.5.4] - 2025-02-19

### Bug Fixes

- Re-adding event as has_many assoc to venue (#604)

### Miscellaneous Tasks

- Bump version to 2.5.4 (#605)

## [2.5.3] - 2025-02-18

### Bug Fixes

- Adjust availability status check to only consider valid_from | bump version to 2.5.3 (#603)

## [2.5.2] - 2025-02-18

### Bug Fixes

- Adding availability status hidden for variants with availability without start and end (#600)

### Miscellaneous Tasks

- Bump version to 2.5.2 (#602)

## [2.5.1] - 2025-02-18

### Features

- Add venue to search event API (#596)

### Bug Fixes

- Update error messages for channel configuration checks (#595)
- Remove venue description and change venue name (#597)
- Use correct state for variables with availability and predecessor (#598)

### Miscellaneous Tasks

- Bump to version 2.5.1 (#599)

## [2.5.0] - 2025-02-14

### Features

- Unique constraint so that only not deleted promoter are considered (#583)
- *(events)* Update event with new chart key after creation (#588)

### Bug Fixes

- *(seats)* Retrieve chart from workspace (#584)
- *(seatsio)* Copy chart to workspace when publishing draft (#587)

### Refactor

- Enhance chart copying logic for event creation and publishing (#585)
- Include promoter in create_seatsio_event function (#586)
- Streamline chart retrieval handling in event controller (#592)
- Move chart property to the correct event structure (#593)

### Miscellaneous Tasks

- Update log level (#590)
- Update minor version (#594)

## [2.4.10] - 2025-02-11

### Features

- [**breaking**] Remove sales_periods from database (#581)

### Bug Fixes

- *(!)* Make release_objects work for channels and set chart_key to new chart_key on event create (#580)

### Miscellaneous Tasks

- Update patch version to v2.4.10 (#582)

## [2.4.9] - 2025-02-07

### Bug Fixes

- Adding get_file_url for thumbnailURL in /api/promoter/events (#578)

### Miscellaneous Tasks

- Bump version to 2.4.9 (#579)

## [2.4.8] - 2025-02-05

### Miscellaneous Tasks

- Promoter onboarding muss not have a transfer instrument in order for the address to be updated (#576)
- Bump to version 2.4.8 (#577)

## [2.4.7] - 2025-02-03

### Bug Fixes

- Adding unleash check enable_payout_invoice_generation to maybe_publish_payout_invoice_pdf (#574)

### Miscellaneous Tasks

- Bump version to 2.4.7 (#575)

## [2.4.6] - 2025-02-03

### Bug Fixes

- Add email userinfo for invitations (#572)

### Miscellaneous Tasks

- Update patch version to v2.4.6 (#573)

## [2.4.5] - 2025-01-31

### Bug Fixes

- *(transfers)* Prevent invoice creation for venues outside germany (#570)

### Miscellaneous Tasks

- Bump version to v2.4.5 (#571)

## [2.4.4] - 2025-01-27

### Features

- Filter deleted invoice from list (#567)

### Bug Fixes

- Add deleted dependencies again (#566)

### Miscellaneous Tasks

- Correct flag name (#564)
- Fix Oban version to don't use Elixir 1.18 (#565)
- Bump version 2.4.4 (#568)

## [2.4.3] - 2025-01-22

### Miscellaneous Tasks

- Temporal fix for missing small business flag (#561)
- Bump to version 2.4.3 (#562)

## [2.4.2] - 2025-01-21

### Miscellaneous Tasks

- Disabling promoter invoice generation (#548)
- Bump to version 2.4.2 (#559)

## [2.4.1] - 2025-01-20

### Miscellaneous Tasks

- Use GitHub variable to configure reviewer-lottery (#556)
- Remove hard_ticket and external_id from ticket_category (#549)
- Update patch version to v2.4.1 (#557)

## [2.4.0] - 2025-01-17

### Features

- Extent payload for admission_control_mail (#553)

### Bug Fixes

- Promoter address sync with adyen modifications (#550)
- Pattern matching error (#551)
- Update event pubsub msg on variant update (#552)

### Refactor

- Simplify release_object function parameters handling (#554)

### Miscellaneous Tasks

- *(version)* Update version to v2.4.0 (#555)

## [2.3.5] - 2025-01-10

### Features

- Add opentelemetry sampler (#543)

### Bug Fixes

- Use latest elixir action (#544)
- *(config)* Update traces_exporter for prod/dev environment (#545)

### Other

- *(version)* Update version to 2.3.5 in mix.exs (#546)

## [2.3.4] - 2025-01-09

### Miscellaneous Tasks

- Expose content disposition header (#541)

## [2.3.3] - 2025-01-09

### Bug Fixes

- Update Gettext usage in EventsServiceWeb module (#538)

### Miscellaneous Tasks

- Bump version to v2.3.3 (#539)

## [2.3.2] - 2025-01-09

### Features

- Capitalize promoter short code (#535)

## [2.3.1] - 2025-01-09

### Features

- Update invoice name (#526)
- Always cast is small business flag (#533)
- Bump to version 2.3.1 (#534)

## [2.3.0] - 2025-01-08

### Bug Fixes

- Only preload not deleted entries (#530)

### Miscellaneous Tasks

- Update/bump all dependencies (#516)
- Update version to v2.3.0 (#531)

## [2.2.17] - 2025-01-07

### Features

- Add seat category filtering to variant queries (#499)

### Bug Fixes

- *(events)* Disallow chart_key to be updated (#515)
- Don't filter seatsio variants if seats param is not given (#528)
- Consider admission flag in predecessor list (#527)

### Miscellaneous Tasks

- Update patch version to v2.2.17 (#529)

## [2.2.16] - 2025-01-03

### Bug Fixes

- Adjust validation (#524)

### Miscellaneous Tasks

- Upate patch version to v2.2.16 (#525)

## [2.2.15] - 2025-01-02

### Features

- Clean up predecessor list (#522)

### Miscellaneous Tasks

- Update patch version to v2.2.15 (#523)

## [2.2.14] - 2024-12-30

### Features

- New function tu fetch event with fees (#520)

### Bug Fixes

- Deleted fees should not be included in the organizer fees (#519)

### Miscellaneous Tasks

- Bump to version 2.2.14 (#521)

## [2.2.13] - 2024-12-23

### Bug Fixes

- Make download route  restfull (#497)
- Payout subscriber and transfer instruments (#517)

### Miscellaneous Tasks

- Bump to version 2.2.13 (#518)

## [2.2.12] - 2024-12-20

### Bug Fixes

- Make sales channel editable after sales has started (#512)
- Use correct original price (#513)

### Miscellaneous Tasks

- Update patch version (#514)

## [2.2.11] - 2024-12-19

### Bug Fixes

- Use new event_counter and add invitation to event contingent (#506)

### Miscellaneous Tasks

- Update patch version (#511)

## [2.2.10] - 2024-12-19

### Features

- Ticket and event text validations (#505)
- Update allowed text characters for events and tickets (#508)

### Bug Fixes

- Cut URL from path (#509)

### Miscellaneous Tasks

- Update patch version (#510)

## [2.2.9] - 2024-12-19

### Features

- *(services-api)* Add tracking pixel controller and schema
- Refactor tracking pixel handling and improve API schema
- *(router)* Add tracking pixels route
- Promoter create api documentation (#502)
- *(onboard)* Adds optional settings param to onboarding link creation (#501)
- *(onboarding)* Enable allowBankAccountFormatSelection for promoter onboarding (#503)

### Bug Fixes

- Add changeset response to API (#496)
- Expand event counter (#500)

### Miscellaneous Tasks

- Update patch version (#504)

## [2.2.8] - 2024-12-12

### Features

- Don't delete variants that are used in other features (#396)

### Bug Fixes

- Don't try to show promoter if not loaded

### Miscellaneous Tasks

- Update patch version (#494)

## [2.2.7] - 2024-12-10

### Bug Fixes

- *(ticket-category)* Update allowed text characters to include exclamation mark (#488)
- Nil check for seats workspace keys (#491)

### Miscellaneous Tasks

- *(version)* Update version to 2.2.7 (#492)

## [2.2.6] - 2024-12-10

### Features

- Add internal variants api (#462)
- Internal events api (#472)
- Add email data to pubsub payload (#487)

### Bug Fixes

- Allow originalPrice for sales channel creation again (#485)

### Miscellaneous Tasks

- Revert payout invoice subscription (#486)
- Update patch version to v2.2.6 (#490)

## [2.2.5] - 2024-12-09

### Features

- Add text validation for ticket category fields (#422)

### Bug Fixes

- Make sales channel API backwards compatible (#482)
- Allow originalPrice for sales channel creation (#483)
- Add missing pattern clause (#484)

## [2.2.4] - 2024-12-09

### Bug Fixes

- Use original_price for sales channel calculation (#479)
- Document url loading (#480)

### Miscellaneous Tasks

- Bump to version 2.2.4 (#481)

## [2.2.3] - 2024-12-06

### Bug Fixes

- Don't use broken function in transfer dto

### Miscellaneous Tasks

- Update patch version

## [2.2.2] - 2024-12-06

### Bug Fixes

- Remove payout invoice logic

### Miscellaneous Tasks

- Update patch version to v2.2.2

## [2.2.1] - 2024-12-06

### Bug Fixes

- Invoice document access when invoice is not loaded (#477)

### Miscellaneous Tasks

- Revert pubsub publication (#475)
- Add event_id to invoice pubsub (#476)
- Update patch version

## [2.2.0] - 2024-12-06

### Bug Fixes

- No event fee and kickback for extras (#469)

### Miscellaneous Tasks

- Temporal deactivation of the pubsub publication (#473)
- Update minor version to v2.2.0 (#474)

## [2.1.1] - 2024-12-05

### Features

- Add migration and server exit status to docker-start.sh (#457)
- Make ticket counter sync asynchronous (#468)

### Miscellaneous Tasks

- Update ex_service_client (#467)
- Update patch version to v2.1.1 (#470)

## [2.1.0] - 2024-12-04

### Features

- [**breaking**] Remove sales_periods from service (#458)
- Sync all counter for all events (#460)

### Bug Fixes

- Re-add variables from categories to variants (public dto) (#463)
- Use correct variable order (#464)
- Preload ticket category (#465)

### Miscellaneous Tasks

- Update minor version to v2.1.0 (#466)

## [2.0.2] - 2024-12-02

### Features

- Sync account on account holder information change on adyen side (#452)

### Bug Fixes

- Return correct http status (#455)

### Miscellaneous Tasks

- Update patch version to v2.0.2 (#459)

## [2.0.1] - 2024-11-29

### Features

- Replace event_id with event title and start date in transfer de… (#448)
- Add short code to promoters (#449)

### Bug Fixes

- Change default visibility for sales channels (#450)
- Expand the error_to_text function (#451)
- Not offer variants with not_offerd predecessor (#453)

### Miscellaneous Tasks

- Update patch version v2.0.1 (#454)

## [2.0.0] - 2024-11-26

### Features

- Add worker for SalesAutomation (#424)
- Add filter to variantsAPI, show only possible predecessors (#426)
- [**breaking**] Sync sales_periods and availabilities
- Get all possible predecessors for new variants (#444)
- [**breaking**] Delete unused ticket categories (#443)

### Bug Fixes

- Add visible to public price combinations (#425)
- Remove SalesChannel APIs from SalesChannel list
- Add preloads to variants api (#427)
- Get admission from ticket_category (#428)
- Update SalesChannels (#429)
- Filter by admission on ticket_category (#430)
- Use cheapest gross price instead for ticket base price (#431)
- Add fallback to remaining contingen (#432)
- Add sales_channel changeset
- Validate_predecessor pattern match (#437)
- Add discounted unit_price to sc variants (#438)
- Use correct price for each element
- Remove no longer needed pattern match (#439)
- Fix predecessor validation (#440)
- Only show possible predeccessors with same admisson (#441)
- Allow change or add an predecessor for variants with end_date (#442)
- Remove typo, add preload
- Hide variants with invisible ticket_categories (#446)
- Create new ticket_categories for variants with sales_periods (#435)
- Make seeds working for prod db

### Refactor

- Use all variants for event quota (#423)

### Miscellaneous Tasks

- Remove compiler warning
- Change error message (#445)
- [**breaking**] Update major version (#447)

## [1.11.4] - 2024-11-21

### Features

- Add cheapestPrice and salesStatus for 'new' variants (#414)
- Extend SalesChannel variant API (#416)
- Only use variants with admission true for salesStatus and cheapestPrice (#418)

### Bug Fixes

- Add future demand fee to creation mail products (#415)
- Use correct status for variants with predecessor and valid_until (#417)
- Checking for same variant_id in invitation update api (#419)
- Add empty list fallback (#420)

### Miscellaneous Tasks

- Bump version to 1.11.4 (#421)

## [1.11.3] - 2024-11-20

### Bug Fixes

- Adjust changeset (#413)

## [1.11.2] - 2024-11-20

### Bug Fixes

- Swap join so select is not needed (#412)

## [1.11.0] - 2024-11-20

### Features

- Add compatability for send event creation mail for variants … (#399)
- Upate extras with variants API (#400)
- Change sales channels URLs (#398)
- Change variant APIs, remove experimental flag (#402)
- Change sorting for public variants API. (#405)
- Add validation for availability and predecessor (#406)

### Bug Fixes

- Return only grossPrice and not the whole variant (#397)
- Add seatsio_keys preload to job + `get_promoter_by_event_id`(#401)
- Use correct join and presale_start_date for public visility of variants
- Use correct aliases/names for swagger docu (#407)
- Don't update predecessor if it is nil
- Use correct pattern match (#408)
- Allow mark as sold out with predecessor (#409)

### Miscellaneous Tasks

- Update elixir and erlang (#394)

## [1.10.5] - 2024-11-15

### Bug Fixes

- Remove typo and unused attribute (#388)
- Add variant validation on invitation update | refactor invitatio… (#390)
- Add sales channel contingent to event contingent (#392)

### Miscellaneous Tasks

- Update patch version (#393)

## [1.10.4] - 2024-11-15

### Features

- *(variant_counter)* Expand variant counter with sold_items, reserved_items, exising_items (#387)

### Bug Fixes

- *(variants)* Add admission filter to organizer context
- *(variants)* Add admission filter to variants API, organizer context (#385)
- *(variants)* Add named binding to admission filter (#386)

### Miscellaneous Tasks

- Add git cliff to gitgnore (#377)
- Update patch version to v1.10.4 (#389)

## [1.10.3] - 2024-11-12

### Bug Fixes

- *(variants)* Use correct visibility state (#382)

### Miscellaneous Tasks

- Update patch version to v1.10.3 (#383)

## [1.10.2] - 2024-11-12

### Bug Fixes

- Use same visibility state (#378)

### Miscellaneous Tasks

- Update patch version to v1.10.2

## [1.10.1] - 2024-11-11

### Bug Fixes

- Add all tables to sales channels search query (#379)
- Don't try to check a float with a string (#380)

### Miscellaneous Tasks

- Update patch version

## [1.10.0] - 2024-11-11

### Features

- *(platform_fee)* Add platform fee for events (#361)

### Bug Fixes

- Use correct pattern match to show or delete a SalesChannel (#375)

### Miscellaneous Tasks

- Upate README (#373)
- Update minor version to v1.10.0 (#376)

## [1.9.0] - 2024-11-11

### Features

- *(entrance-area)* Validate uses on delete (#364)
- *(successor)* Add list of successor IDs to variant API response for organizer (#370)

### Bug Fixes

- *(entrance-area)* Always check for entrance area job uniqueness (#367)
- *(entrance-area)* Filter sales channel quota (#366)
- *(entrance-area)* Is visible null check (#369)
- *(variants)* Hide secret information in public response (#368)
- Add better validation for public price visibility (#371)
- Add event_id to availability

### Miscellaneous Tasks

- *(entrance-area)* Include some special chars (#365)
- Bump version to v.1.9.0 (#374)

## [1.8.0] - 2024-11-06

### Features

- *(variants)* Extend variants index API (#357)
- Add Jan to Reviewer Lottery (#358)
- *(entrance-area)* Internal index endpoint (#360)

### Bug Fixes

- Add proper pattern match for invitation_controller.ex (#354)
- Use empty string as sales period name, if sales period doesn't exist (#359)
- Add category flag 'allEntranceAreasAllowed' (#362)

### Miscellaneous Tasks

- Use experimental permission api (#355)
- Bump version to v1.8.0 (#363)

## [1.7.10] - 2024-10-31

### Features

- *(variant)* Add new status to variants for the organizer context (#350)

### Miscellaneous Tasks

- Update patch version to v1.7.9 (#352)
- Update patch version to v1.7.10 (#353)

## [1.7.9] - 2024-10-30

### Features

- Remove user_id from access check (#349)

### Bug Fixes

- Create variant error handling (#347)
- Use internal variants response to send event creation mail (#351)

## [1.7.8] - 2024-10-28

### Features

- Filter by user access (#344)
- Add availability and status to internal events response (#346)

### Miscellaneous Tasks

- Update patch version to v1.7.8 (#348)

## [1.7.7] - 2024-10-28

### Miscellaneous Tasks

- Fix pattern match (#345)

## [1.7.6] - 2024-10-28

### Bug Fixes

- Add more error handling on new validate legal entity (#342)

### Miscellaneous Tasks

- Bump version 1.7.6 (#343)

## [1.7.5] - 2024-10-28

### Features

- Add validate_legal_entity by eventId (#331)
- Send admission control pdf with entrance area user (#312)
- *(entrance-area)* Permission check (#330)
- Add entrance area filter (#337)
- Add entrance area name to admission pdf payload (#340)

### Bug Fixes

- Show active variants if valid_until is nil (#334)
- Process scheduled oban jobs (#335)
- Add pagination infos to variant APIs (#336)
- Allow variants with predecessor to be upcoming (#339)
- Check event permission before delete a variant (#338)

### Miscellaneous Tasks

- Remove sensible information from response (#319)
- Bump version 1.7.5 (#341)

## [1.7.4] - 2024-10-23

### Bug Fixes

- Change event_id for sales channels (#333)

## [1.7.3] - 2024-10-23

### Features

- *(ticket_workflow)* Add public variants API (#326)

### Bug Fixes

- Add verify header to variants route (#328)
- Use camelCase for API response (#329)

### Other

- Slug sc support (#332)

### Refactor

- Move app envs to runtime config (#318)

## [1.7.2] - 2024-10-22

### Documentation

- Add missing examples for 'new' variants (#323)

### Miscellaneous Tasks

- Stream data in dev (#324)
- Update patch verstion to v1.7.2 (#325)

## [1.7.1] - 2024-10-22

### Features

- *(entrance-area)* Publish ticket categories update (#307)
- Add entrance area to ticket category (#316)

### Bug Fixes

- CamelCase venueName (#321)

### Documentation

- Add variant API documentation (#315)

### Miscellaneous Tasks

- Add log if slug generation did not succeed (#320)
- Bump version 1.7.1 (#322)

## [1.7.0] - 2024-10-22

### Features

- Mark sales phases APIs as deprecated (#298)

### Bug Fixes

- Typo (#311)

### Miscellaneous Tasks

- Update minor version to v1.7.0 (#314)

## [1.6.13] - 2024-10-18

### Features

- *(entrance-area)* Crud apis (#295)
- *(entrance-area)* Extend by user document id filter (#297)
- Add ticket categories on entrance area create (#302)
- *(entrance-area)* Attach authToken (#303)
- *(entrance-area)* Add security user event permission (#304)
- Validate permissions (#299)
- Allow entrance area create without ticket categories (#309)

### Bug Fixes

- Get single security user for event (#305)
- *(entrance-area)* Allow numbers in text (#308)
- *(saleschannels)* Increase default page size of sales channels (#310)

### Miscellaneous Tasks

- Bump version to v1.6.13

## [1.6.12] - 2024-10-09

### Features

- Add status to variant (#292)

### Bug Fixes

- Add nil check for sales periods (#293)

### Miscellaneous Tasks

- Update patch version to v1.6.12 (#294)

## [1.6.11] - 2024-10-02

### Features

- *(variant)* Update variants without sales periods (#289)

### Bug Fixes

- Failed payout transaction handling (#291)

### Miscellaneous Tasks

- Update patch version
- Event create V2 api documentation (#290)

## [1.6.9] - 2024-09-30

### Features

- Add order_no to variant (#286)

### Bug Fixes

- Add CORS configuration (#287)
- Remove typo

### Miscellaneous Tasks

- Update patch version to v1.7.9

## [1.6.8] - 2024-09-27

### Features

- Sort variants by unit_price

### Miscellaneous Tasks

- Update patch version to v1.6.8

## [1.6.7] - 2024-09-25

### Features

- Make predecessor an foreign key (#281)

### Miscellaneous Tasks

- Update patch version to v1.6.7

## [1.6.6] - 2024-09-24

### Features

- *(events,svc,gke)* Add annotated gke deployment [SI-225][SI-220] (#285)

### Miscellaneous Tasks

- Revert fetch window to 1 day (#284)

## [1.6.5] - 2024-09-24

### Features

- Migrate dynamics link click count to postgres sql (#282)

### Miscellaneous Tasks

- Bump to version 1.6.5

## [1.6.4] - 2024-09-24

### Miscellaneous Tasks

- Update patch version to v1.6.4

## [1.6.3] - 2024-09-24

### Features

- Use DbSeeds instead of PhilColumns (#280)

### Bug Fixes

- Upload transformed webp file instead of original (#278)
- Do not delete admission and end date (#279)

### Miscellaneous Tasks

- Update patch verstion to v1.6.3

## [1.6.2] - 2024-09-23

### Bug Fixes

- Make kickback deleteable (#277)

### Miscellaneous Tasks

- Update patch version to v1.6.2

## [1.6.1] - 2024-09-23

### Features

- Add variants index API for variants without sales_period (#273)

### Miscellaneous Tasks

- Update patch version to v1.6.1

## [1.6.0] - 2024-09-23

### Features

- Add presaleStartDate to variant (#272)

### Miscellaneous Tasks

- Update Elixir to 1.17 (#274)
- Upate minor version to v1.6.0

## [1.5.2] - 2024-09-20

### Features

- Event owner should not add himself in the permission list (#269)
- Add default sales periods to the event creation again (#270)

### Bug Fixes

- Use deprecated parameter for deprecated event creation flow (#271)

### Miscellaneous Tasks

- Update patch version to v1.5.2

## [1.5.1] - 2024-09-20

### Bug Fixes

- Change permission check

### Refactor

- Change names for the visibility state from variants (#268)

### Miscellaneous Tasks

- Update patch version to v1.5.1

## [1.5.0] - 2024-09-19

### Features

- Extend guestlist_invitation_deleted_mail.ex properties | add… (#267)

### Bug Fixes

- Change fallback controller default response from 404 to 400 (#264)

### Miscellaneous Tasks

- Upate minor version to v1.5.0

## [1.4.15] - 2024-09-17

### Bug Fixes

- Change page size default from 10 to 100

### Miscellaneous Tasks

- Update patch version to v1.4.15

## [1.4.14] - 2024-09-17

### Bug Fixes

- Hide variants with invisible categories
- Remove typo

### Miscellaneous Tasks

- Update patch version to v1.4.14

## [1.4.13] - 2024-09-17

### Bug Fixes

- Send event update msg on event update (#265)

### Miscellaneous Tasks

- Update version to 1.4.13

## [1.4.12] - 2024-09-16

### Bug Fixes

- Add empty description to create adyen payload

## [1.4.11] - 2024-09-16

### Bug Fixes

- Use jason encode for event update (#257)

### Miscellaneous Tasks

- Update version to 1.4.11

## [1.4.10] - 2024-09-13

### Features

- Revert returning 400 bad request (#261)

### Bug Fixes

- Change service account loading (#258)
- Publish event creation and send event creation mail only when th… (#259)
- Only send event creation mail on published events (#260)

## [1.4.9] - 2024-09-11

### Bug Fixes

- Remove setting `e.is_approved: true`
- Add `Ecto.Query` to seed

## [1.4.8] - 2024-09-06

### Features

- Add db connection heartbeat (#255)

## [1.4.7] - 2024-09-06

### Bug Fixes

- Escape filename for ics export (#254)

### Miscellaneous Tasks

- Update patch version to v1.4.7

## [1.4.6] - 2024-09-04

### Bug Fixes

- Use uuid for the event_id in payout_transactions table and schema (#252)

### Miscellaneous Tasks

- Bump patch version to 1.4.6

## [1.4.5] - 2024-09-03

### Features

- New create api for event with just basic informations as all ot… (#245)

### Other

- Remove unused code and fix address validation (#251)

### Miscellaneous Tasks

- Update patch version to v1.4.5

## [1.4.4] - 2024-09-02

### Miscellaneous Tasks

- Bump version to 1.4.4

## [1.4.3] - 2024-08-30

### Bug Fixes

- Remove typo

### Miscellaneous Tasks

- Update patch version to v1.4.3

## [1.4.2] - 2024-08-30

### Bug Fixes

- Add nil check for variant attributes
- Update patch version to v1.4.2

## [1.4.1] - 2024-08-30

### Bug Fixes

- Add nil check to venue creation process (#249)

### Miscellaneous Tasks

- Update patch version to v1.4.1

## [1.4.0] - 2024-08-30

### Features

- *(country)* Return supported country for venue creation (#241)

### Miscellaneous Tasks

- Bump version to 1.4.0

## [1.3.2] - 2024-08-28

### Bug Fixes

- Set default values for visibility before and after sales (#247)

### Miscellaneous Tasks

- Bump to version 1.3.2

## [1.3.0] - 2024-08-23

### Features

- Create offers context and availiability

### Miscellaneous Tasks

- Update minor version to v1.3.0

## [1.2.61] - 2024-08-22

### Features

- Extend event schema relations and returned service call event (#239)
- Future demand event sync (#135)

### Miscellaneous Tasks

- Use a different prepare attrs function since we use a mix of c… (#233)
- Bump version to 1.2.61

## [1.2.60] - 2024-08-14

### Bug Fixes

- *(adyen)* Check reference is a uuid before querying the database (#238)

### Miscellaneous Tasks

- Bump to version 1.2.60

## [1.2.59] - 2024-08-06

### Bug Fixes

- *(event)* Allow null value for end and admission date (#237)

### Miscellaneous Tasks

- Bump to version 1.2.59

## [1.2.58] - 2024-08-05

### Bug Fixes

- *(api)* Add admission flag for "tickets"? (#232)
- *(sample-ticket)* Show placeholders (#234)

### Miscellaneous Tasks

- Update version to v1.2.58

## [1.2.57] - 2024-07-31

### Other

- Remove unused country_id columns (#227)

### Miscellaneous Tasks

- Update patch version to v1.2.57

## [1.2.56] - 2024-07-26

### Bug Fixes

- Prevent owner permission delete (#231)

### Miscellaneous Tasks

- Update version to 1.2.56

## [1.2.55] - 2024-07-25

### Bug Fixes

- *(api)* Hide owner permission (#230)

### Miscellaneous Tasks

- Update version to 1.2.55

## [1.2.54] - 2024-07-24

### Bug Fixes

- Only update changed values for a variant (#229)

### Miscellaneous Tasks

- Update patch version to v1.2.54

## [1.2.53] - 2024-07-24

### Bug Fixes

- *(variant)* Include sold parameter
- Sold parameter for variant counter not prloaded

### Miscellaneous Tasks

- Update version to 1.2.53

## [1.2.52] - 2024-07-24

### Miscellaneous Tasks

- Bump to version 1.2.52

## [1.2.51] - 2024-07-24

### Features

- Allow to remove sales phase limitation (#225)

### Miscellaneous Tasks

- Update patch version to v1.2.51

## [1.2.50] - 2024-07-24

### Features

- Change country fk to iso in address and donation recipient (#226)

### Miscellaneous Tasks

- Update patch version to v1.2.50

## [1.2.49] - 2024-07-17

### Bug Fixes

- Preload country for all users in the promoter area (#222)

### Miscellaneous Tasks

- Update patch version to v1.2.49

## [1.2.48] - 2024-07-17

### Miscellaneous Tasks

- Bump to version 1.2.48

## [1.2.47] - 2024-07-16

### Styling

- Add comments to BEv2 tables (#220)

### Miscellaneous Tasks

- Update patch version to v1.2.47

## [1.2.46] - 2024-07-15

### Features

- Elixir action
- Add reviewer lottery (#215)

### Bug Fixes

- Enhance spotify token api response handling (#212)
- Warnings..
- Schema definition
- Enhance create_signin_qr_code error handling, returning nil
- Add create_custom_token pattern match for missing security_user
- Logger inspect | spec correction
- Restrict price modification on extra/variant when ticket have be… (#219)

### Miscellaneous Tasks

- Update credo
- Format
- Format
- Format
- Remove CODEOWNERS, it's no longer needed because of using reviewer lottery
- Bump to version 1.2.46

## [1.2.44] - 2024-07-04

### Bug Fixes

- Add EventsServiceWeb.ApiSchemas.ExtraSchema.Extra import to extra_controller

### Miscellaneous Tasks

- Bump to version 1.2.44

## [1.2.43] - 2024-07-04

### Miscellaneous Tasks

- Update patch version to v1.2.43

## [1.2.42] - 2024-07-03

### Other

- Move location endpoint

## [1.2.41] - 2024-07-02

### Features

- Activate vouchers by default (#206)
- Fix ticket category closed event check (#186)

### Bug Fixes

- Filter deleted fees out (#205)
- Fetching payout transactions on newly created events (#207)
- Ticket wallet response (#208)

### Miscellaneous Tasks

- Bump version to 1.2.41

## [1.2.40] - 2024-06-28

### Bug Fixes

- Filter for visible events in maybe_delete_sales_periods (#203)

## [1.2.39] - 2024-06-26

### Bug Fixes

- Add index to event permissons table

### Miscellaneous Tasks

- Bump version to v1.2.39

## [1.2.38] - 2024-06-21

### Features

- Elixir-styler (#198)

### Bug Fixes

- *(promoter)* Apply correct name mapping on promoter creation
- Adding possibility to upload list of files for seatin_plans (#188)
- Adding timestamp_type :utc_datetime to Repo config | changing invitation history inserted_at to timestamp(0) (#187)
- Adding nil pattern match for maybe_put_name (#200)

### Miscellaneous Tasks

- Remove unused functinos from AssetsValidator (#199)

## [1.2.37] - 2024-06-19

### Miscellaneous Tasks

- Update Logger
- Update patch version to v1.2.37

## [1.2.36] - 2024-06-18

### Miscellaneous Tasks

- Bump service to version 1.2.36

## [1.2.35] - 2024-06-18

### Miscellaneous Tasks

- Remove permissions from internal event json
- Bump version to 1.2.35

## [1.2.34] - 2024-06-17

### Bug Fixes

- Add project id to bigquery job
- Add missing import
- Add closedAt to event dto

### Miscellaneous Tasks

- Cleanup most warnings
- Clean up some more warnings
- Add csv
- Add closedDate to promoter event  overview
- Bump version to 1.2.34

## [1.2.33] - 2024-06-10

### Features

- Re revert event link to events

### Miscellaneous Tasks

- Bump events-service to version 1.2.33

## [1.2.32] - 2024-06-06

### Bug Fixes

- Adding nil check for admission flag set

### Miscellaneous Tasks

- Bump version to 1.2.32

## [1.2.31] - 2024-06-06

### Features

- Add missing get event by id function

### Bug Fixes

- Missing repo
- Default admission value

### Miscellaneous Tasks

- Mix format
- Update version to 1.2.31

## [1.2.30] - 2024-06-06

### Features

- All in snake_case

### Miscellaneous Tasks

- Bump to version 1.2.30

## [1.2.29] - 2024-06-06

### Bug Fixes

- Set right permissions for actions

### Miscellaneous Tasks

- Return camelCase responses
- Bump to version 1.2.29

## [1.2.28] - 2024-06-06

### Features

- Unify SalesChannelController error responses | mix format | enhance logging
- Add redeemed_extras to event_counters schema
- Add migration for redeemed_extras
- Support ticket/extras variants in the available/checked in Event properties
- Add redeemed_extras to sync
- Internal events index api

### Bug Fixes

- Remove typo
- Handling percentage discount as integer values (1000 as 10%)
- Enable variant quota modifications
- Quota can be set to greater or equal to sold amount
- Take admission flag directly from variants
- Admission not from ticket_category
- Weird naming for variants

### Refactor

- Remove duplicated code from merge

### Miscellaneous Tasks

- Adding inspect to logger calls
- Mix deps.get
- Rename multi name
- Rename multi name
- Filter internal sales period out
- Filter already deleted variants from the query
- Bump events-service to version 1.2.28
- Use event details
- Move variant inactive check to function

## [1.2.27] - 2024-05-16

### Miscellaneous Tasks

- Bump version to 1.2.27

## [1.2.26] - 2024-05-14

### Features

- *(BEv2)* Create product entities and modules

### Bug Fixes

- Use keyword list in application.ex
- Move cofnig to read it during compiling

### Miscellaneous Tasks

- Make PR changes, change mandatory fields
- Switch to secrets library
- Bump version to 1.2.26

## [1.2.25] - 2024-05-13

### Bug Fixes

- Closed event filters hiding promoter events

### Miscellaneous Tasks

- Bump event to version 1.2.25

## [1.2.24] - 2024-05-10

### Features

- Migrate VariantChannelConfig to SalesChannel
- Add sold attribute for easier restrictions implementation
- Add more error handling
- Remove useless preload
- Filter sales channel from channel config deletion job
- Enhance error handling  | add function docs
- Add todo comments with jira id

### Bug Fixes

- Enhance ticket_category_validation | add ticket_category_id to variant update changeset | add comments for saleschannel crud
- Adding error response objects for sales-channel/tickets api
- Move variant fee calculation to get_event_promoter_fee | add promoter fees to sales channel offers
- Re-adding fee addition to originial price
- Revert event details url for backward compatibility

### Miscellaneous Tasks

- Typos in sales_channel name
- Ran format

## [1.2.23] - 2024-05-07

### Bug Fixes

- Add support for promoter from CZ

## [1.2.22] - 2024-05-07

### Miscellaneous Tasks

- Bump version to 1.2.22

## [1.2.21] - 2024-05-03

### Features

- Adding is_bookable field
- Extends event finalization to set sales periods end date on closing event and filter all closed events from discovery page
- Remove unused bookable changeset
- Total sales statistics
- Add public as secondary db schema

### Bug Fixes

- Multi update_all typo

### Miscellaneous Tasks

- Add Code owners to event-service
- PR changes and comment fixes
- Change local Phoenix port
- Set ex_firebase_auth_plug environment to local in dev config
- Update db connection
- Bump version to 1.2.21

## [1.2.20] - 2024-04-29

### Features

- Replace spotify api forward to backendv2 with SpotifyController

### Miscellaneous Tasks

- Resolve pr comments
- Apply PR changes
- Bump version to 1.2.20

## [1.2.19] - 2024-04-26

### Features

- Finalize payout with option to payout 100% of available balance
- Add assets api template | add validator | format
- Sort mix.exs dependencies | add google_api_storage
- Set mine type of uploaded image to "image/webp
- Rework asset api structure

### Miscellaneous Tasks

- Remove debug code
- Cleanup
- Format
- Credo
- Refactor image upload process
- Solve compiler warnings
- Simplify VerifyAccess permission check
- Solve compiler warnings
- Bump service_client version to 0.1.48 | fetch environment from env | bump version to 1.2.19

## [1.2.18] - 2024-04-23

### Features

- Validate description min length
- Validate description min length
- Working on the sales channel api
- Sales channel api documentation finish

### Bug Fixes

- Setting environment param in runtime via env
- Revert ENVIRONMENT env changes

### Miscellaneous Tasks

- Remove minikube config
- Bump ex_firebase_auth version to 0.2.13
- Bump versino to 1.2.18

## [1.2.17] - 2024-04-18

### Refactor

- Use TCP/IP DB Connection

### Miscellaneous Tasks

- Update patch version

## [1.2.16] - 2024-04-18

### Bug Fixes

- Pagination with offset and sorting

## [1.2.15] - 2024-04-16

### Bug Fixes

- Switching from variant.sold to variant_counter.sold
- Adding VariantCounter alias

### Miscellaneous Tasks

- Bump version to 1.2.15

## [1.2.14] - 2024-04-12

### Bug Fixes

- Handle variant_counter updates without variant_id

### Miscellaneous Tasks

- Bump version to 1.2.14

## [1.2.13] - 2024-04-11

### Features

- Use virtual field to keep track of the frontend generated uuid

### Bug Fixes

- Fix ticket category creation bug
- Fix ticket category creation bug
- Filter by event_id

### Miscellaneous Tasks

- Added some context information
- Renamed external id into frontend id since it is used already in fusion
- Pr changes requests
- Change requests
- Added spec and ran formatter
- Bump to version 1.2.13

## [1.2.12] - 2024-04-09

### Bug Fixes

- Move variants query for search_events to preload | add variant_count preload
- Adding invitation validation for variant_id | fixing variant_id param to order-service

### Miscellaneous Tasks

- Move swaggerui to /events/api | generate api doc
- Bump version to 1.2.12

## [1.2.11] - 2024-04-09

### Features

- Add sold tickets info to event details for promtoer

### Bug Fixes

- Event update pubsub

### Miscellaneous Tasks

- Remove IO.inspect
- Clean up code
- Resolve change requests
- Bump version to 1.2.11

## [1.2.10] - 2024-04-08

### Miscellaneous Tasks

- Bump to version 1.2.10

## [1.2.9] - 2024-04-08

### Features

- Trim and validate empty description field
- Add restrictions to ticket categories and variants
- Internal ticket category api
- Send event data

### Bug Fixes

- Admission control mail not sent
- Use variant counter in event_helper
- Adding hard_ticket and admission flags to invitation order creation | Resolve compiler warnings
- Exclude extra from base price
- Show price for extras if no admission variants
- Remove trim from  subtitle since it is not a required field
- Server error when trimming nullable fields
- SCHMALLE DER BACKEND BOI
- Fix variant sold reference
- Fix variant sold reference check for empty counter
- Fix variant sold reference check for empty counter

### Miscellaneous Tasks

- Ran formatter
- Merge main into feature branch
- Use Jason
- Bump version to 1.2.9

## [1.2.8] - 2024-04-04

### Bug Fixes

- Use variant counter in event_helper

### Refactor

- Change log level

### Miscellaneous Tasks

- Bump version to 1.2.8

## [1.2.7] - 2024-04-04

### Bug Fixes

- Use left_join in sql queries
- Use variant_counter instead of variant.sold
- Preload variant_counter for events
- Enum.earch to Enum.each

### Miscellaneous Tasks

- Bump version to 1.2.7

## [1.2.6] - 2024-04-03

### Features

- Add event and variant counters
- Add voucher_counters table and schema
- Add event, variant and voucher counters
- Remove old unused scheduler
- Add pubsub subscripiton
- Use events counter for popular events
- Add soldTicket and soldExtra to promoter API
- Remove unused counter
- Use new ticket counter sync for the scheduler
- Consume cloud scheduler job module
- Create read update delete sales phases with restrictions api implementation and documentation
- Use resources shorthand function
- Update permission level
- Create read update delete fees with restrictions api implementation and documentation
- Add access verification
- Use resources shorthand function
- Update permission level

### Bug Fixes

- Use unique index for event_id in counter tables
- Use string for pubsub topic
- Use right alias
- Make some changes after debugging on fusion
- Add add_if_not_exists

### Miscellaneous Tasks

- MOVE PUBLIC FUNCTIONS ABOVE PRIVATE FUNCTIONS
- Add comments + plural of types
- Outsource some big map creations
- Clean up unused alias
- Resolve change requests
- Resolve merge conflicts
- Bump version to 1.2.6

## [1.2.5] - 2024-03-28

### Bug Fixes

- Only check if diff was changed
- Compare changes not && but ||

### Miscellaneous Tasks

- Remove IO.inspect
- Bump patch version to v1.2.5

## [1.2.4] - 2024-03-27

### Features

- Use account service

### Bug Fixes

- Moving Mix.Project version to module variable

### Miscellaneous Tasks

- Update service client version to 0.1.32
- Update ex_service_client to 0.1.33
- Update ex_service_client to 0.1.33
- Bump version to 1.2.4

## [1.2.3] - 2024-03-21

### Bug Fixes

- Add inspect to logs | bump version to 1.2.3

## [1.2.2] - 2024-03-21

### Bug Fixes

- Add null check for promoter address

### Miscellaneous Tasks

- Bump version to 1.2.2

## [1.2.1] - 2024-03-21

### Bug Fixes

- Add POST body do scheduler

### Miscellaneous Tasks

- Bump version to 1.2.1

## [1.2.0] - 2024-03-20

### Features

- Finalize events
- Send pubsub for each closed event
- Use environment env
- Add tf code to create scheduler
- Add properties to ticket categories
- Add properties to ticket categories
- Add is_visible to changeset
- Add extras properties to changeset
- Add admission/externalId-/provider to DTO
- Only create variant if quota & unit_price != 0
- Min admission tickets
- Add category hint
- Add hint to ticket category
- Ticket type for category
- Return isVisible in variants from categories
- Remove min_admission_tickets_amount migration
- First time event publication publish message in bus when event is created

### Bug Fixes

- Remove copy and paste error
- Use right pubsub topic
- Use a new changeset to finalize events
- Cut decimals for percentage output
- Bring 2 decimals back to percentage output
- Use id from params when creating new salesPeriod/ticketCategory
- Only check if diff was changed
- Compare changes not && but ||
- Add category hint
- Add ticket type to dtos
- Venue short
- Venue short
- Add isVisible to getEditEvent

### Refactor

- Remove typo in logging output
- Clean up code

### Miscellaneous Tasks

- Change default value for events pubsub topic
- Resolve schon PR change requests
- Deps
- Remove IO.inspect
- Update minor version to v1.2.0

## [1.1.23] - 2024-03-18

### Bug Fixes

- Use right pattern match

## [1.1.22] - 2024-03-18

### Miscellaneous Tasks

- Add some debug logs

## [1.1.21] - 2024-03-11

### Features

- Add channel configs
- Create channels
- Update channel config
- Remove redundant variant_id association
- Remove redundant variant_id association
- Add event_id index to channel_config migration
- Adjust channel_config schema
- Add sync and renewToken route
- Sync / renewToken functionality
- Add delete channel route
- Add amount_of_objects to channel config
- Add amount_of_objects to channel config
- Add delete channel config
- Add channel configs to event
- Fetch promoter directly from current_user_id for update/delete ChannelConfig
- Get channel by token
- Bump ex_seatsio
- Add delete_expired_channels
- Add channel configs api doc
- Add proper error handling
- Change cloud scheduler APIs from rpc to rest
- Replace image with data
- Add ticket data to mail

### Bug Fixes

- Routes
- Schema prefix
- Route
- 'fixed' instead of 'flat' in ChannelConfig
- Channel rpc api to rest
- Use event data
- Spelling
- Promoter name

### Miscellaneous Tasks

- Move create channel config to one transaction
- PR review feedback
- Bump deps
- Bump version to 1.1.21

## [1.1.20] - 2024-03-08

### Miscellaneous Tasks

- Add cache for /events (index)
- Bump version to 1.1.20

## [1.1.19] - 2024-03-07

### Bug Fixes

- Update ticket_counter for all variants

### Miscellaneous Tasks

- Clean up
- Update patch version v1.1.19

## [1.1.18] - 2024-03-05

### Miscellaneous Tasks

- Bump version to 1.1.18

## [1.1.17] - 2024-03-05

### Miscellaneous Tasks

- Bump version to 1.1.17

## [1.1.16] - 2024-03-04

### Features

- Add admissionDate to events overview
- Remove base image, update elixir and opt

### Bug Fixes

- Add api doc push to remote repo
- Remove typo

### Miscellaneous Tasks

- Remove IO.inspect
- Update build description and fix git commit message
- Update patch version

## [1.1.15] - 2024-02-23

### Miscellaneous Tasks

- Bump version 1.1.15

## [1.1.14] - 2024-02-22

### Features

- Configure gcp logging
- Extending public invitaiton response

### Bug Fixes

- Re-adding populated user names
- Nil check for attendees

### Other

- Adding check for attendee assoc
- Fixing filter for given_name
- Adding invitation_history date fields
- Adding not loaded check for invitation_history
- Adding removed_by details for invitation_history | Fetching user names from backend
- Adding removed_by details for invitation_history | Fetching user names from backend
- Fix module name of ventsService.Repo.Migrations.AddInvitationHistoryRemoved

### Miscellaneous Tasks

- Update ex_service_client to 0.1.35
- Update ex_service_client to 0.1.43
- *(admission)* Disable counter for special events
- Bump version to 1.1.13
- Bump version to 1.1.14

## [1.1.13] - 2024-02-15

### Features

- Add manage_objects API
- Add not-for-sale APIs
- Add channels to manage_objects
- Add /event API to fetch seatsio event
- Add mark-everything-for-sale and event API
- Bump ex_seatsio 0.1.9

### Miscellaneous Tasks

- Bump deps
- Bump seats.io lib

## [1.1.12] - 2024-02-09

### Features

- Add seat comment documentation
- Add openapi.json
- Admission app only shows the sold tickets and quota of the current sales period

### Bug Fixes

- Credo warning

### Miscellaneous Tasks

- Bump to version 1.1.12

## [1.1.10] - 2024-02-06

### Features

- Bump version

### Bug Fixes

- *(events)* Add permission check to events show_edit API
- Fix privilege escalation

## [1.1.9] - 2024-02-02

### Features

- Fix event edit bug

## [1.1.8] - 2024-01-31

### Features

- Seat comments
- Get seat comments by object ids
- Adjust seat_comment API and add history endpoint
- Use group_key amk
- Feat
- Adjust route for delete comment-group
- *(seat)* Comment delete also deletes also history related
- Get comment groups by key and event_id, not id
- Also return group_key for seat comment groups
- Search by name
- Search by group keys
- Get comments by group keys
- Add group to comment
- Filter by object ids
- Comment history
- History count
- Add names to promoter json
- Add multiple fields to promoter detail json

### Bug Fixes

- Delete not returning {:ok, ..}
- Random group name
- Delete commonts on group delete
- Comments for objects
- Fix
- Fix
- Fix
- Fix
- Show comments for user
- Comment history route
- Spelling
- *(seat)* Change migration comment text from string to text

### Other

- Update and delete seat comment groups

### Refactor

- Get user only once

### Miscellaneous Tasks

- Revert search by object id changes
- Fix warning
- Revert search changes

## [1.1.6] - 2024-01-25

### Features

- Remove stripper
- Update stripper to remove only unused tags
- Update stripper to remove only unused tags
- Add total sales and total checked in counter
- Implement counter fetching and setting
- Change item evaluation
- Merge main in branch
- Add spotify api for artist info and top tracks
- Adjust new spotify API url
- Adding spotify API, controller and client
- Adding spotify API documentation | Format
- Adding spotify API documentation | Format

### Bug Fixes

- Add comma in runtime
- Restore old spotify API functionallity | Rename new apis to /v2/

### Miscellaneous Tasks

- Remove unused code
- Update service client version
- Pr changes
- PR changes
- Result env.template conflict
- Bump to version 1.1.6

## [1.1.5] - 2024-01-24

### Features

- Fix wrong cover url
- Fix wrong cover url

### Miscellaneous Tasks

- Bump to version 1.1.5

## [1.1.4] - 2024-01-23

### Bug Fixes

- *(event)* Adds refund/kickback revenue to internal events API (#76)
- Set kickback default value to 0

### Miscellaneous Tasks

- Bump to version 1.1.4

## [1.1.3] - 2024-01-19

### Bug Fixes

- Oban config

### Miscellaneous Tasks

- Update version

## [1.1.2] - 2024-01-17

### Features

- Add sim search
- Filter the gin indexes for the sim search

### Bug Fixes

- Use right column to filter the index

### Miscellaneous Tasks

- Update patch version to v1.1.2

## [1.1.1] - 2024-01-16

### Features

- Filter deleted tracking links and pixels
- Filter deleted tracking links and pixels
- Filter deleted tracking links and pixels
- Adjusting .env.template to be usable for minikube

### Bug Fixes

- Promoter address from adyen casting

### Miscellaneous Tasks

- Bump to version 1.1.1

## [1.1.0] - 2024-01-15

### Features

- Add seats secrets to dev.exs
- Add seatsio library
- /venues/charts API
- Add seatsio integration to event creation transaction
- *(wip)* Seatsio module, including event creation
- Adjust seatsio event creation code to multi
- Use multi event in seatsIO create event
- Add release_object functionality
- Add hardTicket to ticketCategory DTO
- *(ticket category)* Hard_ticket migration
- Add hard_ticket into schema
- Add seatsio keys to runtime.exs & update deps
- Add workspace_key to public promoter dto
- Create prod workspaces based on ENV
- Add seatsio integration to event creation transaction
- Add release_object functionality
- Add seats secrets to dev.exs
- Add seatsio library
- /venues/charts API
- Add seatsio integration to event creation transaction
- *(wip)* Seatsio module, including event creation
- Adjust seatsio event creation code to multi
- Use multi event in seatsIO create event
- Add release_object functionality
- Add hardTicket to ticketCategory DTO
- *(ticket category)* Hard_ticket migration
- Add hard_ticket into schema
- Add seatsio keys to runtime.exs & update deps
- Add workspace_key to public promoter dto
- Create prod workspaces based on ENV
- Add 'inspect' around stuff being logged/raised

### Bug Fixes

- Use strftime instead of native Timex
- Add missing function maybe_return_ids
- Add missing function maybe_return_ids
- Use strftime instead of native Timex
- Add missing function maybe_return_ids
- Resolve weird rebase/merge stuff
- Re-add  "/:event_id/promoter" route
- Read user-agent for logging

### Other

- Rebase conflicts?

### Miscellaneous Tasks

- Mix format
- Bump seatsio library
- Mix format
- Mix format
- Mix deps.get
- Bump seatsio library
- Mix format
- Update deps
- Set seatsio lib to 0.1.5 in deps
- Update version to 1.1.0

## [1.0.17] - 2024-01-12

### Miscellaneous Tasks

- Bump to version 1.0.17

## [1.0.16] - 2024-01-12

### Features

- Add missing security login qr_code

### Other

- Update patch version to v1.0.16

## [1.0.15] - 2024-01-12

### Bug Fixes

- Filter approved events from popular events list
- Missing event

### Miscellaneous Tasks

- Update patch version to v1.0.15

## [1.0.14] - 2024-01-12

### Features

- Adding minikube env and runtime
- Creating minikube env template
- Correct minikube env variable
- Add envs for minikube runtime
- Working on sync counter
- Finish sync update for variants and vouchers
- Upgrade lib
- Upgrade lib
- Reading from global function

### Bug Fixes

- Missing alias and bump service client version
- Added adyen env to atom conversion
- Make promoter events detail endpoint backwarts compatible

### Other

- Update patch version to v1.0.14

### Miscellaneous Tasks

- Pr changes request

## [1.0.13] - 2024-01-09

### Bug Fixes

- Return voucher
- Filter approved events

### Miscellaneous Tasks

- Fix warning
- Bump version

## [1.0.12] - 2024-01-09

### Bug Fixes

- Show all available variants in another function

## [1.0.11] - 2024-01-09

### Bug Fixes

- Promoter store url

## [1.0.10] - 2024-01-09

### Bug Fixes

- Promoter store url
- Show all available variants

## [1.0.9] - 2024-01-09

### Bug Fixes

- Adding thumbnailPath to to_internal_event_details_dto_data

### Miscellaneous Tasks

- Bump version to 1.0.9

## [1.0.8] - 2024-01-09

### Other

- Add some debug logging infos

## [1.0.7] - 2024-01-09

### Bug Fixes

- Adding thumbnailPath to get_event_by_id function for service calls

### Miscellaneous Tasks

- Bump version to 1.0.7

## [1.0.6] - 2024-01-09

### Bug Fixes

- Old event id not being loaded

### Miscellaneous Tasks

- Update version

## [1.0.5] - 2024-01-09

### Miscellaneous Tasks

- Logging for debug

## [1.0.4] - 2024-01-09

### Miscellaneous Tasks

- Logging for debug

## [1.0.3] - 2024-01-09

### Bug Fixes

- Update adyen config version

## [1.0.2] - 2024-01-09

### Bug Fixes

- Update adyen version
- Update  version

## [1.0.1] - 2024-01-09

### Bug Fixes

- Update version

## [1.0.0] - 2024-01-09

### Features

- Management
- Remove IO inspect log
- Remove IO inspect log
- Config
- Change INSTANCE_UNIX_SOCKET in POSTGRES_HOST
- Rebuild container registry
- Rebuild container registry
- Change running port
- Change exposed port in dockerfile
- Bump adyen to 0.1.6
- Create promoter, promoter users, promoter employees
- Working on promoter
- Working on promoter
- Working on promoter
- Working on remaining promoter endpoints and venue
- Add events schema
- Add event variant schema
- Add Sales period schema and migration
- Create tickets category schema and migration
- Create fee, artist and voucher schema and migration
- Use company credo standard
- Set schema prefix
- Add google cloud configs
- Added event_document_id to vouchers
- Update gcloud dev config
- Add default search enpoint callbacks
- Working on discover page apis
- Promoter events
- Implement tracking pixel
- Refactoring
- Change event permission role type from element to list of role
- Create tracking link schema migration and context
- Remove ExCommonDB deps
- Implement tracking-link
- Update promoter controller for the new permission loading
- Continue with implementation of edge cases
- Updated migration script
- Updated env template
- Fix show error
- Added spotify routes
- Added spotify routes
- Added spotify routes
- Change image name
- Fix get secret error
- Fix get secret error
- Expose management port
- Event update bug fixes
- Event update bug fixes
- Move migration routes from management router to router file
- Deactivate management endpoint
- Secure migration routes with basic auth
- Upgrade service client version
- Upgrade service client version
- Fix endpoint
- Fix endpoint
- Fix endpoint
- Fix endpoint
- Fix endpoint
- Fix endpoint
- Fix endpoint
- Fix missing ticket cat and sales periods
- Fix file cdn links
- Fix file cdn links
- Fix file cdn links
- Fix file cdn links
- Fix file cdn links
- Fix file cdn links
- Fix popular events
- Fix mobile links
- Fix missing frontend url
- Fix promoter claims not being updated
- Logging actions
- Fixing routing
- Fixing routing
- Fixing routing
- Fixing routing
- Fix order service call
- Add seatsio columns in migrations
- Fix company naming typos
- Add seatsio fieled migration
- Fix get events lists
- Add chart_key to event_json
- Add chart_key to event.ex
- Fix tracking error
- Error handling fix frontend
- Add seatsio workspace keys
- Event publication and un publication fix
- Calendar export
- Debugging mobile requests
- Debugging mobile requests
- Debugging mobile requests
- Debugging mobile requests
- Debugging mobile requests
- Debugging mobile requests
- Fix search error
- Fix event search und events details page
- Fix event search und events details page
- Fix venue search and events search algolia dto
- Fix event promoter
- Promoter event dashboard
- Promoter event dashboard
- Promoter dashboard totals
- Promoter dashboard totals with result from order service
- Promoter dashboard finished
- Apply stash to fix emails
- Added fee price
- PR changes requests
- Implement event creation mail
- Implement sampleTicket and admissionControl template
- Implement sampleTicket and admissionControl template
- Implement event update mail
- Implement event permission changed mail
- Implement event permission changed mail
- Implement event finished mail
- Implement event finished mail
- Apply stash to fix emails
- Translate ecto changeset error to text
- Add Logger configuration
- *(logging)* Switch production logging to JSON
- Adjust promoter migration script
- Adjust migration script for prod release
- Promoter store link
- Batch promoter dashboard
- Bump version to 1.2.24
- Voucher and variant counter pub/sub
- Optimize and fix sql queries
- Use internal carts totals API
- Use new internal cart API
- Add logger for debug purposes
- Remove promoter creation mail
- Secure endpoint with scheduler token
- Add API documentation with UI
- Create and add opanapi.json

### Bug Fixes

- Event trigger branch dev to fusion [SI-15][SD1-1410]
- Ex_firebase_auth_plug env
- Ex_firebase_auth_plug env
- Service client and adyen wrong referencing in transfers context
- Update payout status to failed if we fail to create a payout on adyen side
- Update payout status to failed if we fail to create a payout on adyen side
- Fix update
- Update balance platform auth plug
- Bug fixes
- Fixing bugs
- Fixing bugs
- Config for dev deployment
- User assigned and user events error when user was event admin
- Chart_key -> chartKey in event_json.ex
- Add seatsio_public_workspace_key to event promoter DTO
- Search dto
- Algolia errors params is a string
- Algolia errors params is a string
- Algolia errors params is a string
- Fix box office opening date null
- Missing country in promoter event details
- Calculate ticketBasePrice for each count of active variants
- Fix promoter events routes params
- Add / to events router
- Update client version
- Missing quota
- Ran formatter
- Ran formatter
- Permission resolution not workin missing user
- Get popular events base on size
- Ticket list
- Fix migration for prod data
- Remove compile warnings
- Remove unused infos from events overview
- Payout transaction use events service now and don't need to request events from backendV2 anymore
- Add right ticket_category and sales_period to variant
- Add right ticket_category and sales_period to variant
- Make promoter_json work again
- Add missing quota
- Quick and dirty hotfix
- Tracking pixels and links not correctly returned
- Mails
- Fee error
- Reverted fee calculation
- Page size not send
- Event mail properties fixed
- Add promoter fees to ticket price (grossPrice)
- Check if fees are loaded or not
- Check if event fees are preloaded as list
- Use right name for given parameter
- Add fees to events_overview to calculate cheapest ticket_price
- Event mail properties fixed
- Re-add create_variants
- Using dateTimeUtil for example ticket dates
- Refactor sales periods for event creation mail
- Adding condition to filter empty variant for event creation mail | Adding comment about unused event finished mail
- Adjust expires at calculation for security user token
- Empty query param should return an empty result set
- Remove unused joins
- Update ticket category update
- Get events by ids
- Decouple event creation and event creation notification the event is created but notification is not send and the hole process crashes
- Promoter creation mail is send through an oban job
- Promoter events publication
- Set admissionDate on event creation
- Close information leak in events-detail API
- Add error handling to popular_events API
- Put fees back to the event
- Add deprecated attribute to make API backwarts compatible
- Move deprecated attribute
- Add Logger to Mailer
- Cast Ecto.UUID to String
- Change response back to id
- Send event creation mail again
- Fix logging infos
- *(docker)* Fix docker for graceful shutdown
- Query events additionally by security role | Rename function to find_events_by_user_document_id
- Add thumbnailUrl and venueId to promoter_event_overview_data
- Check if tickets are available
- Changing permission check for promoter_details to view
- Adjust param for :promoter_details endpoint
- Access control
- Fix missing store_url
- Unaliased OrdersService module
- Wrong pattern matching
- Add inspect to all logger calls + format
- Adding subtitle, isApproved and endDate to promoter_event_overview_data
- Adding related events for promoter that are also event_admin | adding brackets for to_edit_event_dto_data kickback
- Convert unit_price from float to integer
- Convert fees and kickback from float to integer
- Remove double envents from promoter overview
- Remove local changes
- Trim user inputs for new venues
- Read cloud proj from env not secrets
- Use the new field displayName
- Voucher counter error
- Oban scheduler route
- Oban scheduler route
- Voucher not found causing crash in checkout process
- Error shown when opening promoter area with a fresh created promoter account
- Fix usage on unlimited voucher
- Version
- Use right alias for API documentation
- Switching git api doc upload command to git checkout "$BRANCH_NAME"

### Other

- Rename cloud run service
- Rename publish pdfTopic types to camelCase
- Adding event creation mail to create event
- Adding attachments to eventCreationMail and body instead of data
- Fix event update mail compare
- Simplify date compare
- Fix with condition
- Fix security user query
- Fix event creation mail errors
- Fix getSecurityUser query
- Remove event secret
- Flatten redundant mail body
- Rename publish pdfTopic types to camelCase
- Adding event creation mail to create event
- Adding attachments to eventCreationMail and body instead of data
- Fix event update mail compare
- Simplify date compare
- Adding ticketsAvailable to promoter/events endpoint

### Refactor

- Use CamelCase for all attributes in the API response
- Resolve change requests
- Resolve change requests
- Load EventsService.OrdersTicketsSubscriber not in local demo projects

### Documentation

- Add some Logging output during event creation
- Add more Loggings into event creation
- Change log level
- Add some more logs

### Miscellaneous Tasks

- Fix compilation warning
- Rename run_local to run-local
- Update mix.lock
- Update ex_service_client
- Remove IO.inspect
- Remove test code
- Fix event_info_to_localized_string casing
- Comment out mails
- Build promoter events counter for looker dashboard
- Uncomment email sending functions
- Ran formatter
- Switch to pdf pubsub topic
- Resolve warnings
- Remove structs
- Cherry-pick finetuning
- Remove IO.inspect
- Remove test code
- Fix event_info_to_localized_string casing
- Ran formatter
- Bump ex_rbac version to 0.1.4
- Remove duplicate function
- End date should not fallback on start date
- Add response status
- Resolve pr change request
- Remove logger_json
- Pr change request
- Working on oban mail
- Working on oban mail
- Fix events service configs
- Remove ex_common_db
- Update ex_service_client
- Remove IO inspect
- Add some logging expressions to follow up pub/sub activities

<!-- generated by git-cliff -->
