defmodule EventsService.Events.EventHelpers do
  @moduledoc false
  import Ecto.Query, warn: false

  alias EventsService.Events
  alias EventsService.Events.EntranceArea
  alias EventsService.Events.Event
  alias EventsService.Events.Fee
  alias EventsService.Events.TicketCategory
  alias EventsService.Events.Variant
  alias EventsService.Events.VariantCounter
  alias EventsService.Promotion.Voucher
  alias EventsService.Token
  alias EventsService.Util.DateTime, as: DateTimeUtil
  alias EventsService.Util.Money, as: MoneyUtil
  alias EventsService.Vendor.Promoter
  alias EventsService.Vendor.SeatsioWorkspaceKeys
  alias EventsServiceWeb.MultiLevelPricingModifierJSON
  alias ExServiceClient.Services.AccountsService
  alias ExServiceClient.Services.OrdersService

  require Logger

  def get_street(nil), do: ""

  def get_street(street_address) do
    [_house_number | rest] = street_address |> String.split(" ") |> Enum.reverse()
    rest |> Enum.reverse() |> Enum.join(" ") |> String.trim()
  end

  def get_house_number(nil), do: ""

  def get_house_number(street_address) do
    [house_number | _rest] = street_address |> String.split(" ") |> Enum.reverse()
    house_number
  end

  def maybe_include_promoter(event_response, _event, false), do: event_response

  def maybe_include_promoter(event_response, event, true),
    do: Map.put(event_response, :promoter, transform_promoter_dto(event.promoter))

  @doc """
  Transforms event permissions into a list of user permissions.

  This function takes an event struct with permissions and the creator's document ID,
  and returns a flattened list of transformed permissions.

  ## Parameters

    - event: A struct containing :permissions, :created_by and :created_by_document_id keys.

  ## Returns

    A list of transformed permissions.
  """
  @spec transform_event_permissions(Event.t()) :: list()
  def transform_event_permissions(event) do
    %Event{
      permissions: permissions,
      created_by_document_id: created_by_document_id,
      created_by: created_by
    } = event

    Enum.flat_map(permissions, &transform_event_permission(&1, created_by_document_id, created_by))
  end

  @doc """
  Transforms a single event permission into a list of user permission maps.

  This function handles different cases:
  1. Ignores permissions for security users (user_document_id starting with "sec_").
  2. Ignores permissions for the event creator.
  3. Fetches user info for other users and creates a permission map.

  ## Parameters

    - permission: A map containing permission details.
    - created_by_document_id: The document ID of the event creator.
    - created_by: The user who created the event.

  ## Returns

    A list containing zero or one permission map.
  """
  @spec transform_event_permission(map(), binary(), map()) :: list(%{email: String.t(), roles: atom(), id: binary()})
  def transform_event_permission(%{user_document_id: "sec_" <> _} = _item, _created_by_document_id, _created_by), do: []

  def transform_event_permission(%{user_document_id: created_by_document_id}, created_by_document_id, _created_by)
      when not is_nil(created_by_document_id),
      do: []

  def transform_event_permission(%{user_id: created_by}, _created_by_document_id, created_by)
      when not is_nil(created_by),
      do: []

  def transform_event_permission(
        %{user_document_id: user_document_id, user_id: user_id, role: role, id: id},
        _created_by_document_id,
        _created_by
      ) do
    case AccountsService.get_user_by_id(user_document_id || user_id) do
      {:ok, userinfo} -> [%{email: userinfo["email"], roles: role, id: id}]
      _ -> []
    end
  end

  def transform_tracking_links(tracking_links) do
    Enum.map(tracking_links, fn tracking_link ->
      %{
        id: tracking_link.id,
        label: tracking_link.label,
        url: tracking_link.url,
        targetUrl: tracking_link.target_url
      }
    end)
  end

  def transform_tracking_pixels(nil), do: []

  def transform_tracking_pixels(tracking_pixels) do
    Enum.map(tracking_pixels, fn tracking_pixel ->
      credentials =
        Enum.map(tracking_pixel.tracking_pixel_credentials, fn credential ->
          %{
            id: credential.id,
            trackingPixelId: credential.tracking_pixel_id,
            type: credential.type,
            value: credential.value
          }
        end)

      %{
        id: tracking_pixel.id,
        eventId: tracking_pixel.event_id,
        label: tracking_pixel.label,
        type: tracking_pixel.type,
        trackingPixelCredentials: credentials
      }
    end)
  end

  def transform_channel_configs(channel_configs) do
    Enum.map(channel_configs, fn channel_config ->
      %{
        id: channel_config.id,
        token: channel_config.token,
        channelKey: channel_config.channel_key,
        type: channel_config.type,
        value: channel_config.value
      }
    end)
  end

  @spec transform_vouchers([Voucher.t()] | nil) :: [map()] | []
  def transform_vouchers(nil), do: []

  def transform_vouchers(vouchers) do
    Enum.map(vouchers, fn voucher ->
      %{
        id: voucher.id,
        code: voucher.code,
        firestore_id: voucher.firestore_id,
        is_active: voucher.is_active,
        description: voucher.description,
        quota: voucher.quota,
        type: voucher.type,
        value: voucher.value,
        limit_per_redemption: voucher.limit_per_redemption
      }
    end)
  end

  @spec get_sales_status([Variant.t()]) :: :ACTIVE | :UPCOMING | :SOLD_OUT | :NOT_OFFERED
  @spec get_sales_status([Variant.t()], [TicketCategory.t()]) :: :ACTIVE | :UPCOMING | :SOLD_OUT | :NOT_OFFERED
  def get_sales_status([]), do: :NOT_OFFERED

  def get_sales_status(variants) when is_list(variants) do
    variants = Enum.filter(variants, fn variant -> variant.ticket_category.admission end)

    cond do
      !is_nil(Enum.find(variants, fn variant -> Map.get(variant, :status) == :ACTIVE end)) -> :ACTIVE
      !is_nil(Enum.find(variants, fn variant -> Map.get(variant, :status) == :UPCOMING end)) -> :UPCOMING
      !is_nil(Enum.find(variants, fn variant -> Map.get(variant, :status) == :SOLD_OUT end)) -> :SOLD_OUT
      true -> :NOT_OFFERED
    end
  end

  def get_sales_status(_variants), do: :NOT_OFFERED

  def get_sales_status(variants, ticket_categories) when is_list(variants) and is_list(ticket_categories) do
    admission_ticket_categories =
      Enum.filter(ticket_categories, fn ticket_category -> ticket_category.admission end)

    variants =
      Enum.filter(variants, fn variant ->
        Enum.any?(admission_ticket_categories, fn ticket_category ->
          ticket_category.id == variant.ticket_category_id
        end)
      end)

    cond do
      !is_nil(Enum.find(variants, fn variant -> Map.get(variant, :status) == :ACTIVE end)) -> :ACTIVE
      !is_nil(Enum.find(variants, fn variant -> Map.get(variant, :status) == :UPCOMING end)) -> :UPCOMING
      !is_nil(Enum.find(variants, fn variant -> Map.get(variant, :status) == :SOLD_OUT end)) -> :SOLD_OUT
      true -> :NOT_OFFERED
    end
  end

  def get_sales_status(_variants, _ticket_categories), do: :NOT_OFFERED

  @doc """
  Transforms variants into internal tickets used by the internal implementation
  """
  def transform_internal_variants_dto(event) do
    variants = event.variants

    Enum.reduce(variants, [], fn
      %{variant_counter: nil} = variant, acc ->
        organizer_fees = get_variant_organizer_fee(event, variant)

        variant =
          event
          |> build_public_variant(variant, variant.quota, organizer_fees)
          |> Map.put(:sold, 0)
          |> Map.put(:unitPrice, variant.unit_price)

        [variant | acc]

      %{variant_counter: %VariantCounter{sold_items: sold_items, reserved_items: reserved_items}} =
          variant,
      acc ->
        remaining = variant.quota - sold_items - reserved_items

        organizer_fees = get_variant_organizer_fee(event, variant)

        variant =
          event
          |> build_public_variant(variant, remaining, organizer_fees)
          |> Map.put(:sold, sold_items + reserved_items)
          |> Map.put(:unitPrice, variant.unit_price)

        [variant | acc]
    end)
  end

  def transform_promoter_basics_dto(%{seatsio_workspace_keys: %SeatsioWorkspaceKeys{} = keys} = promoter, is_draft) do
    workspace_key = get_workspace_key(keys, is_draft)
    build_promoter_basics_dto(promoter, workspace_key)
  end

  def transform_promoter_basics_dto(promoter, _is_draft) do
    build_promoter_basics_dto(promoter)
  end

  def transform_promoter_internal_dto(promoter) do
    %{
      id: promoter.id,
      name: promoter.display_name,
      inserted_at: promoter.inserted_at,
      balanceAccountId: promoter.balance_account_id
    }
  end

  def transform_promoter_dto(%{address: nil} = promoter) do
    %{
      id: promoter.id,
      name: promoter.display_name,
      address: "",
      city: "",
      storeUrl: promoter.store_url,
      streetNumber: "",
      verified: promoter.is_verified || false,
      zipCode: "",
      country: ""
    }
  end

  def transform_promoter_dto(promoter) do
    %{
      id: promoter.id,
      name: promoter.display_name,
      address: get_street(promoter.address.street_address),
      city: promoter.address.locality,
      storeUrl: promoter.store_url,
      streetNumber: get_house_number(promoter.address.street_address),
      verified: promoter.is_verified || false,
      zipCode: promoter.address.postal_code,
      country: get_country_iso_from_address(promoter.address)
    }
  end

  def transform_artist_dto(artists) do
    Enum.map(artists, fn artist ->
      %{id: artist.source_id, label: artist.label, own_id: artist.id}
    end)
  end

  def transform_fee_dto(fees) do
    Enum.map(fees, fn fee ->
      flat_modifier_int = MoneyUtil.to_int(fee.flat_modifier)

      %{
        id: fee.id,
        name: fee.name,
        taxRate: fee.tax_rate,
        # Deprecated: Use flatModifier
        unitPrice: flat_modifier_int && flat_modifier_int / 100,
        flatModifier: fee.flat_modifier,
        percentageModifier: fee.percentage_modifier
      }
    end)
  end

  def get_ticket_category_variants(ticket_category_id, variants) do
    Enum.filter(variants, fn item -> item.ticket_category_id == ticket_category_id end)
  end

  def get_entrance_areas_from_ticket_category(%TicketCategory{
        entrance_area_ticket_categories: entrance_area_ticket_categories
      })
      when is_list(entrance_area_ticket_categories) do
    Enum.flat_map(entrance_area_ticket_categories, fn entrance_area_ticket_category ->
      extract_entrance_areas_from_entrance_area_ticket_category(entrance_area_ticket_category)
    end)
  end

  def get_entrance_areas_from_ticket_category(_ticket_category), do: []

  def get_cheapest_ticket_price([]), do: nil

  def get_cheapest_ticket_price([_head | _rest] = variants) do
    variants
    |> Enum.filter(fn variant -> variant.available == true && variant_admission?(variant) end)
    |> Enum.sort_by(& &1.grossPrice)
    |> Enum.at(0)
    |> maybe_get_gross_price()
  end

  @spec get_cheapest_gross_price([Variant.t()]) :: integer() | nil
  @spec get_cheapest_gross_price([Variant.t()], [Fees.t()]) :: integer() | nil
  def get_cheapest_gross_price([]), do: nil

  def get_cheapest_gross_price(variants) do
    variants
    |> Enum.filter(fn
      %Variant{ticket_category: %TicketCategory{}} = variant ->
        variant.status == :ACTIVE && variant.ticket_category.admission && variant.distribution_type == :REGULAR

      _ ->
        false
    end)
    |> Enum.min_by(fn variant -> Map.get(variant, :gross_price, nil) end, fn -> nil end)
    |> case do
      %{gross_price: nil} -> nil
      # return Cents instead of Euros
      # refactor when use ExMoney library and / or multi currency
      %{gross_price: price} -> price
      _ -> nil
    end
  end

  def get_cheapest_gross_price(event_variants, event_fees) do
    cheapest_variant =
      event_variants
      |> Enum.filter(fn variant ->
        variant.status == :ACTIVE && variant.ticket_category.admission && variant.distribution_type == :REGULAR
      end)
      |> Enum.min_by(fn variant -> variant.unit_price end, fn -> %Variant{unit_price: 0} end)

    cheapest_variant.unit_price + get_variant_organizer_fee(event_fees, cheapest_variant)
  end

  def maybe_get_gross_price(%{grossPrice: gross_price}), do: gross_price
  def maybe_get_gross_price(_variant), do: 0

  def get_donation_recipient(nil), do: nil
  def get_donation_recipient([donation | _rest]), do: donation

  def get_file_url(file) do
    cdn = :events_service |> Application.get_all_env() |> Keyword.get(:cdn)
    gcloud = :events_service |> Application.get_all_env() |> Keyword.get(:gcloud)
    "#{cdn}/#{gcloud}-cdn-bucket/#{file}"
  end

  def to_approve_event_link(event_id) do
    hash_digest = get_event_id_hash(event_id)
    frontend_url = Application.get_env(:events_service, :frontend_url)

    "#{frontend_url}/auth/action?mode=approveEvent&eventId=#{event_id}&code=#{hash_digest}"
  end

  def get_event_id_hash(event_id) do
    hash_digest =
      :crypto.mac(:hmac, :sha256, Application.get_env(:events_service, :ticket_secret), event_id)

    hash =
      :crypto.mac(
        :hmac,
        :sha512,
        Application.get_env(:events_service, :ticket_secret),
        hash_digest
      )

    Kernel.inspect(hash)
  end

  def event_to_ics(_event, nil), do: {:error, :filename_is_required}
  def event_to_ics(_event, ""), do: {:error, :filename_cant_be_an_empty_string}

  def event_to_ics(event, filename) do
    start_date = event.start_date

    end_date =
      case event.end_date do
        nil -> DateTime.add(start_date, 12, :hour)
        end_date -> end_date
      end

    events = [
      %ICalendar.Event{
        summary: event.title,
        dtstart:
          {{start_date.year, start_date.month, start_date.day}, {start_date.hour, start_date.minute, start_date.second}},
        dtend: {{end_date.year, end_date.month, end_date.day}, {end_date.hour, end_date.minute, end_date.second}},
        description: event.description,
        location: get_calendar_venue(event.venue),
        # Fixme: Add revert to `events` asap
        url: "#{Application.get_env(:events_service, :frontend_url)}/events/#{event.id}",
        categories: [event.category],
        status: "CONFIRMED"
      }
    ]

    ics = ICalendar.to_ics(%ICalendar{events: events})

    case File.read(filename) do
      {:ok, _content} -> File.rm(filename)
      {:error, :enoent} -> :ok
    end

    {:ok, File.write!(filename, ics)}
  end

  def get_calendar_venue(nil), do: ""

  def get_calendar_venue(%{address: nil} = _venue), do: ""

  def get_calendar_venue(venue) do
    "#{venue.address.street_address} #{venue.address.postal_code} #{venue.address.locality}"
  end

  @spec get_event_contingent([TicketCategory.t()]) :: integer()
  def get_event_contingent([]), do: 0

  def get_event_contingent(ticket_categories) do
    Enum.reduce(ticket_categories, 0, fn %{quota: ticket_category_quota}, acc ->
      acc + (ticket_category_quota || 0)
    end)
  end

  def build_example_ticket(event) do
    event_ticket_dto =
      event
      |> to_event_ticket_dto()
      |> Map.put("admissionDate", DateTimeUtil.to_default_string(event.admission_date, :datetime))
      |> Map.put("startDate", DateTimeUtil.to_default_string(event.start_date, :datetime))
      |> Map.put("endDate", DateTimeUtil.to_default_string(event.end_date, :datetime))
      |> put_venue_short()

    {:ok,
     %{
       admission: true,
       allEntranceAreasAllowed: false,
       event: event_ticket_dto,
       validFor: "Max Mustermann",
       total: "X,XX",
       categoryName: "Ticketkategorie",
       pricingLevelLabel: "Preisstufe",
       id: "7769f795-5976-45d8-b91f-94811f999f9b",
       purchaseDate: DateTimeUtil.to_default_string(DateTime.utc_now(), :datetime),
       seat: event.chart_key && "Block X, Sitz X",
       categoryHint: "Das ist eine Information auf deinem Ticket",
       entranceAreas: [%{name: "Eingangsbereich"}],
       isSampleTicket: true
     }}
  end

  @spec get_remaining_contingent([Variant.t()]) :: integer()
  def get_remaining_contingent(variants) do
    Enum.reduce(variants, 0, fn
      %{quota: nil}, acc ->
        acc

      %{
        quota: quota,
        variant_counter: %{sold_items: sold_items, reserved_items: reserved_items}
      },
      acc ->
        acc + (quota - sold_items - reserved_items)

      %{
        quota: quota
      },
      acc ->
        acc + quota

      %{}, acc ->
        acc
    end)
  end

  def event_info_to_localized_string(string) do
    case string do
      "title" -> "Titel"
      "description" -> "Beschreibung"
      "start_date" -> "Startdatum"
      "end_date" -> "Enddatum"
      "admission_date" -> "Einlassdatum"
      "admission_time" -> "Einlasszeit"
      "box_office_opening_date" -> "Abendkasse"
      "ticket_price" -> "Ticketpreis"
      "ticket_link" -> "Ticketlink"
      "venue_id" -> "Veranstaltungsort"
      _ -> string
    end
  end

  def event_info_to_readable_string(event, key) do
    data = event[key]

    case key do
      "start_date" ->
        to_date_time_string(data)

      "end_date" ->
        if is_nil(data) do
          "Open End"
        else
          to_date_time_string(data)
        end

      "admission_date" ->
        if is_nil(data) do
          to_date_time_string(event["start_date"])
        else
          to_date_time_string(data)
        end

      "box_office_opening_date" ->
        if is_nil(data) do
          "Nicht verfügbar"
        else
          to_date_time_string(data)
        end

      _ ->
        data
    end
  end

  def get_cover_url(event) do
    if event.cover_url do
      get_file_url(event.cover_url)
    else
      get_file_url(event.thumbnail_url)
    end
  end

  def get_event_total_sales(event_ids) do
    case OrdersService.calculate_total_sales_by_event_ids(%{"event_ids" => event_ids}) do
      {:ok, %{"total_sales" => total}} -> total
      _ -> []
    end
  end

  def get_sold_count(event) do
    Enum.reduce(event.variants, 0, fn item, acc ->
      acc + get_variant_sold(item)
    end)
  end

  def create_signin_qr_code(event) do
    with {:security_user, %{user_document_id: user_document_id, user_id: user_id} = _security_user} <-
           {:security_user, Events.get_security_user_by_event_id(event.id)},
         {:token, {:ok, signin_qr_code, _claims}} <-
           {:token, Token.create_custom_token(event.start_date, event.end_date, user_document_id || user_id)} do
      Logger.debug("Security user  login code #{inspect(signin_qr_code)}")
      signin_qr_code
    else
      {:security_user, nil} ->
        Logger.critical("Event #{inspect(event.id)} is missing security user")
        nil

      {:token, error} ->
        Logger.error(
          "Failed to generate signin qr code token for event #{inspect(event.id)} because of error #{inspect(error)}"
        )

        nil
    end
  end

  @spec get_variant_organizer_fee(event :: Event.t() | Ecto.UUID.t() | [Fees.t()], variant :: Variant.t()) :: integer()
  def get_variant_organizer_fee(event, variant) when is_binary(event),
    do: event |> Event.get_event_with_fees() |> get_variant_organizer_fee(variant)

  def get_variant_organizer_fee(%Event{fees: fees} = _event, variant) when is_list(fees),
    do: get_variant_organizer_fee(fees, variant)

  def get_variant_organizer_fee(fees, variant) when is_list(fees),
    do: fees |> Enum.map(fn fee -> calculate_variant_fee(fee, variant) end) |> Enum.sum() |> round()

  def get_variant_organizer_fee(_event, _variant), do: 0

  @spec get_promoter_name(Promoter.t()) :: String.t()
  def get_promoter_name(%Promoter{given_name: given_name, family_name: family_name})
      when not is_nil(given_name) and not is_nil(family_name),
      do: "#{given_name} #{family_name}"

  def get_promoter_name(%Promoter{display_name: display_name}) when not is_nil(display_name), do: display_name

  def get_promoter_name(%Promoter{company_name: company_name}) when not is_nil(company_name), do: company_name

  def get_promoter_name(_promoter), do: "Unknown"

  defp calculate_variant_fee(%Fee{flat_modifier: flat_modifier}, _variant) when not is_nil(flat_modifier),
    do: MoneyUtil.to_int(flat_modifier)

  defp calculate_variant_fee(%Fee{percentage_modifier: percentage_modifier}, %Variant{unit_price: unit_price})
       when not is_nil(percentage_modifier) do
    unit_price * percentage_modifier
  end

  defp calculate_variant_fee(_fee, _variant), do: 0

  defp get_workspace_key(%{public_test_workspace_key: test_key, public_key: prod_key}, is_draft) do
    if is_draft, do: test_key, else: prod_key
  end

  defp build_promoter_basics_dto(promoter, workspace_key \\ nil) do
    %{
      id: promoter.id,
      name: promoter.display_name,
      storeUrl: promoter.store_url,
      workspaceKey: workspace_key,
      # deprecated attribute for backwards compatibility
      country: ""
    }
  end

  defp to_event_ticket_dto(event) do
    has_name? = event.promoter.given_name && event.promoter.family_name

    name = event.promoter.company_name
    name = (has_name? && "#{event.promoter.given_name} #{event.promoter.family_name}") || name
    name = event.promoter.display_name || name

    %{
      id: event.id,
      title: event.title,
      subtitle: event.subtitle,
      admissionDate: event.admission_date,
      startDate: event.start_date,
      endDate: event.end_date,
      venue: EventsServiceWeb.VenueJSON.to_venue_dto_data(event.venue),
      promoterName: name
    }
  end

  defp get_country_iso_from_address(nil), do: ""

  defp get_country_iso_from_address(%{country: nil} = _address), do: ""

  defp get_country_iso_from_address(%{country: country} = _address), do: country.iso

  defp put_venue_short(event_ticket_dto) do
    Map.put(
      event_ticket_dto,
      "venueShort",
      "#{event_ticket_dto.venue.name}, #{event_ticket_dto.venue.street}  #{event_ticket_dto.venue.houseNumber}, #{event_ticket_dto.venue.zipCode} #{event_ticket_dto.venue.city}"
    )
  end

  defp to_date_time_string(date) do
    Timex.format(date, "%d:%m:%Y %H:%M")
  end

  defp get_variant_sold(%{variant_counter: nil}), do: 0
  defp get_variant_sold(%{variant_counter: variant_counter}), do: variant_counter.sold

  # TODO: refactor or remove, is only used for internal communication
  defp build_public_variant(event, variant, remaining, organizer_fees) do
    entrance_areas = get_entrance_areas_from_ticket_category(variant.ticket_category)

    # styler:sort
    %{
      admission: variant.ticket_category.admission,
      allEntranceAreasAllowed: all_entrance_areas_allowed?(event, entrance_areas),
      available: remaining > 0,
      description: variant.ticket_category.description,
      entranceAreas: entrance_areas,
      externalId: variant.ticket_category.external_id,
      externalProvider: variant.ticket_category.external_provider,
      grossPrice: calculate_gross_price(variant, organizer_fees) / 100,
      hint: variant.ticket_category.hint,
      id: variant.ticket_category.id,
      isVisible: variant.ticket_category.is_visible,
      maxAmount: variant.max_amount,
      minAmount: variant.min_amount,
      multiLevelPricingModifier: get_mlpm_data(variant.mlpm),
      name: variant.ticket_category.name,
      quota: variant.quota || 0,
      remaining: remaining,
      salesChannelId: get_sales_channel_id(variant),
      salesChannelName: get_sales_channel_name(variant),
      taxRate: variant.ticket_category.tax_rate,
      ticketType: variant.ticket_category.ticket_type,
      variantId: variant.id
    }
  end

  defp calculate_gross_price(%Variant{ticket_category: %{admission: false}, unit_price: unit_price}, _organizer_fee),
    do: unit_price

  defp calculate_gross_price(%Variant{unit_price: unit_price}, organizer_fee) when is_number(organizer_fee),
    do: round(unit_price + organizer_fee)

  defp calculate_gross_price(_variant, _organizer_fee), do: 0

  defp get_sales_channel_id(%{sales_channel: %{id: id}}), do: id
  defp get_sales_channel_id(_variant), do: nil

  defp get_sales_channel_name(%{sales_channel: %{channel_config: %{label: label}}}), do: label
  defp get_sales_channel_name(_variant), do: nil

  defp extract_entrance_areas_from_entrance_area_ticket_category(%{entrance_area: %EntranceArea{id: id, name: name}}),
    do: [%{id: id, name: name}]

  defp extract_entrance_areas_from_entrance_area_ticket_category(_entrance_area_ticket_category), do: []

  defp variant_admission?(%{admission: admission}), do: admission
  defp variant_admission?(%{ticket_category: %{admission: admission}}), do: admission
  defp variant_admission?(_), do: true

  defp all_entrance_areas_allowed?(_event, []), do: true

  defp all_entrance_areas_allowed?(%{entrance_areas: []} = _event, _category_entrance_areas), do: true

  defp all_entrance_areas_allowed?(%{entrance_areas: event_entrance_areas} = _event, category_entrance_areas) do
    length(event_entrance_areas) == length(category_entrance_areas)
  end

  defp get_mlpm_data([]), do: nil

  defp get_mlpm_data([modifier | _] = _mlpm_list), do: MultiLevelPricingModifierJSON.public_show(%{modifier: modifier})

  defp get_mlpm_data(_mlpm), do: nil
end
