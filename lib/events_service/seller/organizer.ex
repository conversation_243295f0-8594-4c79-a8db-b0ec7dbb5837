defmodule EventsService.Seller.Organizer do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  alias EventsService.Seller.Seller
  alias EventsService.Vendor.Promoter

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          promoter: Promoter.t(),
          promoter_id: Ecto.UUID.t(),
          display_name: String.t() | nil,
          deleted_at: DateTime.t() | nil,
          seller: Seller.t() | nil,
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "organizers" do
    belongs_to :promoter, Promoter

    field :deleted_at, :utc_datetime
    field :display_name, :string

    has_one :seller, Seller, foreign_key: :type_id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(organizer, attrs) do
    organizer
    |> cast(attrs, [:promoter_id, :display_name, :deleted_at])
    |> validate_required([:promoter_id])
  end
end
