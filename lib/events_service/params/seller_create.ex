defmodule EventsService.Params.SellerCreate do
  @moduledoc """
  Seller creation params
  """
  use Ecto.Schema

  import Ecto.Changeset

  alias EventsService.Params.AddressCreate

  # Allowed text characters for name and description.
  # Only letters, numbers, spaces, and the following special characters are allowed:
  # + # & - /
  @allowed_text_characters ~r/^[\p{L}\p{N}\s\+#&\-\/]+$/u

  @seller_types ~w(STORE ORGANIZER PLATFORM SHOP POS RESELLER)a
  @required_fields ~w(type owner_id)a
  @optional_fields ~w(parent_id)a

  @primary_key false
  # styler:sort
  embedded_schema do
    embeds_one :seller_data, SellerData, primary_key: false do
      embeds_one :address, AddressCreate
      field :name, :string
      field :promoter_id, Ecto.UUID
      field :display_name, :string
    end

    field :owner_id, Ecto.UUID
    field :parent_id, Ecto.UUID
    field :type, Ecto.Enum, values: @seller_types
  end

  @doc """
  Creates a changeset for seller creation parameters.

  Validates required fields and type-specific data based on the seller type.
  """
  @spec changeset(map()) :: Ecto.Changeset.t()
  def changeset(params) do
    type = params["type"] || params[:type]

    %__MODULE__{}
    |> cast(params, @required_fields ++ @optional_fields)
    |> validate_required(@required_fields)
    |> validate_inclusion(:type, @seller_types)
    |> cast_embed(:seller_data,
      with: fn schema, params -> seller_data_changeset(schema, params, type) end,
      required: true
    )
  end

  defp seller_data_changeset(schema, params, type) do
    schema
    |> cast(params, [:name, :promoter_id, :display_name])
    |> apply_type_specific_validations(type)
  end

  defp apply_type_specific_validations(changeset, "STORE") do
    changeset
    |> validate_format(:name, @allowed_text_characters)
    |> cast_embed(:address, with: &AddressCreate.changeset/2, required: true)
  end

  defp apply_type_specific_validations(changeset, "ORGANIZER") do
    changeset
    |> validate_required([:promoter_id])
    |> validate_format(:display_name, @allowed_text_characters)
  end

  defp apply_type_specific_validations(changeset, _) do
    add_error(changeset, :type, "unsupported seller type")
  end
end
