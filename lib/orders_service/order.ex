defmodule OrdersService.Order do
  @moduledoc """
  Order schema and changeset.
  """

  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Ecto.Multi
  alias ExServiceClient.Services.EventsService.Event
  alias OrdersService.Bill
  alias OrdersService.EmailBlacklist
  alias OrdersService.InvitationOrder
  alias OrdersService.Locations.Address
  alias OrdersService.Order
  alias OrdersService.OrderHistory
  alias OrdersService.OrderTicket
  alias OrdersService.PayinTransaction
  alias OrdersService.PersonalInformation
  alias OrdersService.Repo
  alias OrdersService.Ticket
  alias OrdersService.Tickets.TicketToken.TicketTokenParser
  alias OrdersService.TicketSwap.SwapTransaction
  alias OrdersService.Workers.SeatsReleaseWorker

  require Logger

  @similarity_threshold 0.3

  @order_statuses [
    :AWAITING_PAYMENT,
    :CREATED,
    :DEFRAUDED,
    :FAILED,
    :INVITATION_REJECTED,
    :PAID,
    :PENDING,
    :REFUND_PENDING,
    :REFUNDED,
    :TIMEDOUT
  ]

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  @derive {Jason.Encoder, only: [:id, :status, :submitted_date]}
  # styler:sort
  schema "orders" do
    belongs_to :bill, Bill
    belongs_to :billing_address, Address, foreign_key: :billing_address_id
    belongs_to :created_by_personal_information, PersonalInformation, foreign_key: :created_by
    belongs_to :delivery_address, Address, foreign_key: :delivery_address_id
    field :deleted_at, :utc_datetime
    field :email, :string
    field :order_item_count, :integer, virtual: true
    field :paid_date, :utc_datetime
    field :payer_email, :string
    field :receipt_number, :string
    field :seller_id, :binary_id
    field :status, Ecto.Enum, values: @order_statuses
    field :submitted_date, :utc_datetime
    field :total, :integer, virtual: true
    has_many :invitation_orders, InvitationOrder
    has_many :order_tickets, OrderTicket
    has_many :payin_transactions, PayinTransaction
    has_many :swap_transactions, SwapTransaction, foreign_key: :parent_order_id
    has_one :payin_transaction, PayinTransaction
    timestamps()
  end

  @type t :: %Order{
          bill_id: binary(),
          billing_address_id: binary(),
          deleted_at: NaiveDateTime.t(),
          delivery_address_id: binary(),
          email: String.t(),
          id: binary(),
          paid_date: NaiveDateTime.t(),
          payer_email: String.t(),
          seller_id: binary() | nil,
          status: atom(),
          submitted_date: NaiveDateTime.t(),
          inserted_at: NaiveDateTime.t(),
          updated_at: NaiveDateTime.t()
        }

  def changeset(order, attrs) do
    order
    |> cast(attrs, [
      :bill_id,
      :created_by,
      :email,
      :paid_date,
      :payer_email,
      :seller_id,
      :receipt_number,
      :status,
      :submitted_date,
      :billing_address_id,
      :delivery_address_id,
      :deleted_at
    ])
    |> validate_required([:status])
    |> validate_inclusion(:status, @order_statuses)
  end

  def create(attrs \\ %{}) do
    %Order{}
    |> Order.changeset(attrs)
    |> Repo.insert()
  end

  def update(order, attrs) do
    order
    |> Order.changeset(attrs)
    |> Repo.update()
  end

  def set_status(order, status, user_id \\ nil, invitation_id \\ nil) do
    case execute_status_change(order, status, user_id, invitation_id) do
      {:ok, order} ->
        maybe_queue_seats_release(order, status)
        {:ok, order}

      {:error, error} ->
        Logger.error("Failed to set order status to #{inspect(status)}: #{inspect(error)}")
        {:error, error}
    end
  end

  def get(order_id, prelaods \\ []) do
    Repo.one(from(o in Order, where: o.id == ^order_id, preload: ^prelaods))
  end

  def get_and_lock_for_update_query(order_id) do
    from(o in Order,
      where: o.id == ^order_id,
      lock: "FOR UPDATE NOWAIT"
    )
  end

  def all do
    Repo.all(Order)
  end

  def get_all_with_status(status \\ [:PENDING]) do
    Repo.all(from(o in Order, where: o.status in ^status))
  end

  def get_by_ticket_id(ticket_id) do
    Repo.one(
      from(o in Order,
        inner_join: ot in OrderTicket,
        on: ot.order_id == o.id and ot.ticket_id == ^ticket_id
      )
    )
  end

  @spec get_all_by_event_id_and_status(binary(), [atom()], [atom()]) :: [Order.t()]
  def get_all_by_event_id_and_status(event_id, status, preloads \\ []) do
    query =
      from(o in Order,
        inner_join: ot in assoc(o, :order_tickets),
        inner_join: t in assoc(ot, :ticket),
        where: t.event_id == ^event_id,
        where: o.status in ^status,
        distinct: o.id,
        select: o,
        preload: ^preloads
      )

    Repo.all(query)
  end

  @spec get_order_ids_by_category_ids([binary()], [atom()], [atom()]) :: [binary()]
  def get_order_ids_by_category_ids(category_ids, status \\ [], ticket_status \\ []) do
    query =
      from(o in Order,
        inner_join: ot in assoc(o, :order_tickets),
        inner_join: t in assoc(ot, :ticket),
        where: t.category_id in ^category_ids,
        where: t.status in ^ticket_status,
        where: o.status in ^status,
        distinct: o.id,
        select: o.id
      )

    Repo.all(query)
  end

  def get_paid_by_id(order_id) do
    query =
      from(o in Order,
        where: o.id == ^order_id,
        where: o.status == ^"PAID",
        where: is_nil(o.deleted_at)
      )

    Repo.one(query)
  end

  def get_all_paid_by_event_id_and_email(event_id, email) do
    query =
      from(o in Order,
        join: ot in OrderTicket,
        on: ot.order_id == o.id,
        join: t in Ticket,
        on: ot.ticket_id == t.id,
        where: t.event_id == ^event_id,
        where: ilike(o.email, ^email),
        where: is_nil(o.deleted_at),
        where: o.status == ^"PAID"
      )

    Repo.all(query)
  end

  @spec get_by_event_id(binary(), binary(), [atom()]) :: struct() | nil
  def get_by_event_id(id, event_id, preloads \\ []) do
    processed_preloads = process_preloads_for_tickets(preloads, event_id)

    query =
      from(o in Order,
        inner_join: ot in assoc(o, :order_tickets),
        inner_join: t in assoc(ot, :ticket),
        where: is_nil(o.deleted_at),
        where: o.id == ^id,
        where: t.event_id == ^event_id,
        select: o,
        limit: 1,
        preload: ^processed_preloads
      )

    Repo.one(query)
  end

  @spec paginate_all(map()) :: Scrivener.Page.t()
  def paginate_all(params) do
    query =
      order_list_query()
      |> where(^filter_where_params(params))
      |> having(^filter_having_params(params))
      |> order_by(^order_by_params(params))

    # Count total_entries separately to avoid issues with group_by clause
    # This ensures accurate pagination when the main query uses aggregations
    count_query =
      query
      |> remove_preload()
      |> remove_order_by()
      |> exclude(:select)
      |> select([o], o.id)

    total_entries = Repo.one(from(q in subquery(count_query), select: count("*")))

    params = Map.put(params, :options, %{total_entries: total_entries})

    query
    |> Repo.paginate(params)
    |> attach_events()
  end

  @spec build_delete_all_multi(Multi.t()) :: Multi.t()
  def build_delete_all_multi(multi) do
    Multi.delete_all(multi, :orders, fn %{order_tickets: {_, order_tickets}} ->
      order_ids = Enum.map(order_tickets, & &1.order_ids)

      from(o in __MODULE__,
        where: o.id in ^order_ids,
        select: %{bill_ids: o.bill_id, personal_information_ids: o.billing_address_id}
      )
    end)
  end

  def count_vouchers_uses(voucher_ids) do
    query =
      from(o in Order,
        inner_join: ot in assoc(o, :order_tickets),
        left_join: t in assoc(ot, :ticket),
        where: is_nil(o.deleted_at),
        where: o.status in [:PAID, :PENDING, :REFUND_PENDING],
        where: t.voucher_id in ^voucher_ids,
        group_by: [t.voucher_id],
        select: %{voucher_id: t.voucher_id, uses: count(fragment("DISTINCT ?", o.id))}
      )

    Repo.all(query)
  end

  defp attach_events(%{entries: entries} = page) do
    event_ids =
      entries
      |> Enum.map(&event_id_from_order/1)
      |> Enum.reject(&is_nil/1)
      |> Enum.uniq()

    events =
      Map.new(event_ids, fn event_id ->
        case Event.get(event_id, [:venue]) do
          {:ok, event} -> {event_id, event}
          _ -> {event_id, nil}
        end
      end)

    updated_entries =
      Enum.map(entries, fn order ->
        event_id = event_id_from_order(order)
        event = event_id && Map.get(events, event_id)
        {order, event}
      end)

    %{page | entries: updated_entries}
  end

  # Returns the event ID associated with an order.
  # Should only be used if filtered and grouped by event id before
  defp event_id_from_order(%Order{order_tickets: order_tickets}) do
    order_tickets
    |> Enum.map(fn
      %{ticket: %{event_id: event_id}} -> event_id
      _ -> nil
    end)
    |> List.first()
  end

  defp event_id_from_order(_), do: nil

  defp execute_status_change(order, status, user_id, invitation_id) do
    Repo.transaction(
      fn ->
        with {:ok, updated_order} <- update_status(order, status),
             :ok <- process_status(updated_order, status, user_id, invitation_id),
             {:ok, _history} <- OrderHistory.create(order, status, user_id) do
          updated_order
        else
          error -> Repo.rollback(error)
        end
      end,
      timeout: 30_000
    )
  end

  defp update_status(order, status) do
    order
    |> Order.changeset(%{status: status})
    |> Repo.update()
  end

  defp maybe_queue_seats_release(%Order{id: order_id}, status) when status in [:FAILED, :TIMEDOUT] do
    if has_seat_tickets?(order_id) do
      queue_seats_release(order_id)
    else
      :ok
    end
  end

  defp maybe_queue_seats_release(_result, _status), do: :ok

  defp queue_seats_release(order_id) do
    case SeatsReleaseWorker.queue_order(order_id) do
      {:ok, _job} ->
        Logger.info("Queued ticket release for order #{order_id}")
        :ok

      error ->
        Logger.critical("Failed to queue ticket release for order #{order_id}: #{inspect(error)}")
        error
    end
  end

  defp has_seat_tickets?(order_id) do
    query =
      from(o in Order,
        inner_join: ot in OrderTicket,
        on: o.id == ot.order_id,
        inner_join: t in Ticket,
        on: ot.ticket_id == t.id,
        where: ot.order_id == ^order_id,
        where: is_nil(o.deleted_at),
        where: not is_nil(t.seat)
      )

    Repo.exists?(query)
  end

  defp order_list_query do
    refunded_orders_subquery =
      from(ot in OrderTicket,
        join: t in Ticket,
        on: ot.ticket_id == t.id,
        where: t.status == :REFUNDED,
        distinct: true,
        select: ot.order_id
      )

    from(o in Order,
      inner_join: cb in assoc(o, :created_by_personal_information),
      as: :created_by_personal_information,
      inner_join: ot in assoc(o, :order_tickets),
      inner_join: t in assoc(ot, :ticket),
      as: :ticket,
      inner_join: a in assoc(t, :attendee),
      as: :attendee,
      inner_join: b in assoc(ot, :bill),
      as: :ticket_bill,
      left_join: ro in subquery(refunded_orders_subquery),
      on: ro.order_id == o.id,
      as: :refunded_order,
      where: is_nil(o.deleted_at),
      group_by: [o.id, cb.id, t.event_id],
      preload: [order_tickets: [:ticket]],
      select: %{
        o
        | created_by_personal_information: cb,
          order_item_count: count(t.id),
          total: sum(b.total)
      }
    )
  end

  defp filter_where_params(params) do
    Enum.reduce(params, dynamic(true), fn {key, value}, acc -> filter_where(acc, key, value) end)
  end

  # Filter tickets based on various criteria.
  # Note: The value parameter can also be an enum.
  defp filter_where(dynamic, :query, search_query) do
    searchable_component = TicketTokenParser.extract_searchable_component(search_query)

    dynamic(
      [order, created_by_personal_information: created_by, ticket: ticket, attendee: attendee],
      ^dynamic and ^build_search_conditions(searchable_component)
    )
  end

  defp filter_where(dynamic, :event_id, value), do: dynamic([ticket: t], ^dynamic and t.event_id == ^value)

  defp filter_where(dynamic, :ticket_status, value), do: dynamic([ticket: t], ^dynamic and t.status in ^value)

  defp filter_where(dynamic, :min_submitted_date, value), do: dynamic([o], ^dynamic and o.submitted_date >= ^value)

  defp filter_where(dynamic, :max_submitted_date, value), do: dynamic([o], ^dynamic and o.submitted_date <= ^value)

  defp filter_where(dynamic, :refunded_items, true), do: dynamic([refunded_order: ro], ^dynamic and not is_nil(ro))

  defp filter_where(dynamic, :refunded_items, false), do: dynamic([refunded_order: ro], ^dynamic and is_nil(ro))

  defp filter_where(dynamic, :seller_id, value), do: dynamic([o], ^dynamic and o.seller_id == ^value)

  defp filter_where(dynamic, _key, _value), do: dynamic

  defp filter_having_params(params) do
    Enum.reduce(params, dynamic(true), fn {key, value}, acc -> filter_having(acc, key, value) end)
  end

  defp filter_having(dynamic, :min_total, value), do: dynamic([ticket_bill: tb], ^dynamic and sum(tb.total) >= ^value)

  defp filter_having(dynamic, :max_total, value), do: dynamic([ticket_bill: tb], ^dynamic and sum(tb.total) <= ^value)

  defp filter_having(dynamic, :min_order_item_count, value),
    do: dynamic([ticket: t], ^dynamic and count(t.id) >= ^value)

  defp filter_having(dynamic, :max_order_item_count, value),
    do: dynamic([ticket: t], ^dynamic and count(t.id) <= ^value)

  defp filter_having(dynamic, _key, _value), do: dynamic

  defp order_by_params(%{order: order_dir} = params) when order_dir in [:desc, :asc],
    do: order_by_params(params, order_dir)

  defp order_by_params(params), do: order_by_params(params, :desc)

  defp order_by_params(%{sort: :name}, order_dir) do
    [
      {order_dir, dynamic([created_by_personal_information: pi], pi.given_name)},
      {order_dir, dynamic([created_by_personal_information: pi], pi.family_name)}
    ]
  end

  defp order_by_params(%{sort: :order_item_count}, order_dir), do: [{order_dir, dynamic([ticket: t], count(t.id))}]

  defp order_by_params(%{sort: :total}, order_dir), do: [{order_dir, dynamic([ticket_bill: tb], sum(tb.total))}]

  defp order_by_params(%{sort: sort}, order_dir), do: [{order_dir, sort}]
  defp order_by_params(_params, order_dir), do: [{order_dir, :submitted_date}]

  defp process_status(_order, :INVITATION_REJECTED, user_id, invitation_id),
    do: Ticket.set_invitation_rejected_by_invitation_id(invitation_id, user_id)

  defp process_status(order, :DEFRAUDED, user_id, _invitation_id), do: EmailBlacklist.create(order.email, user_id)

  defp process_status(order, :TIMEDOUT, user_id, _invitation_id),
    do: Ticket.set_status_by_order_id(order.id, :TIMEDOUT, user_id)

  defp process_status(order, :FAILED, user_id, _invitation_id),
    do: Ticket.set_status_by_order_id(order.id, :FAILED, user_id)

  defp process_status(_order, _status, _user_id, _invitation_id), do: :ok

  defp remove_preload(%Ecto.Query{preloads: _} = query), do: %{query | preloads: []}
  defp remove_preload(query), do: query

  defp remove_order_by(%Ecto.Query{order_bys: _} = query), do: %{query | order_bys: []}
  defp remove_order_by(query), do: query

  defp build_search_conditions({:match, search_query}) do
    dynamic(
      [ticket: ticket],
      fragment("? ILIKE ?", ticket.short_uuid, ^"%#{search_query}%")
    )
  end

  defp build_search_conditions({:no_match, search_query}) do
    dynamic(
      [order, created_by_personal_information: created_by, ticket: ticket, attendee: attendee],
      ^exact_matches(search_query) or ^similarities(search_query)
    )
  end

  defp exact_matches(search_query) do
    dynamic(
      [order, ticket: ticket],
      fragment("? ILIKE ?", order.email, ^"%#{search_query}%") or
        fragment("?::text ILIKE ?", order.id, ^"%#{search_query}%") or
        fragment("?::text ILIKE ?", ticket.id, ^"%#{search_query}%") or
        fragment("?::text ILIKE ?", ticket.category_id, ^"%#{search_query}%")
    )
  end

  defp similarities(search_query) do
    dynamic(
      [order, created_by_personal_information: created_by, attendee: attendee],
      fragment("similarity(?, ?) > ?", ^search_query, order.email, @similarity_threshold) or
        fragment("similarity(?, ?) > ?", ^search_query, created_by.given_name, @similarity_threshold) or
        fragment("similarity(?, ?) > ?", ^search_query, created_by.family_name, @similarity_threshold) or
        fragment("similarity(?, ?) > ?", ^search_query, attendee.given_name, @similarity_threshold) or
        fragment("similarity(?, ?) > ?", ^search_query, attendee.family_name, @similarity_threshold)
    )
  end

  defp process_preloads_for_tickets(preloads, event_id) do
    Enum.map(preloads, fn
      :order_tickets ->
        create_filtered_tickets_preload(event_id, [])

      {:order_tickets, nested} ->
        create_filtered_tickets_preload(event_id, nested)

      other ->
        other
    end)
  end

  defp create_filtered_tickets_preload(event_id, nested) do
    merged = Enum.uniq([:ticket | List.wrap(nested)])

    {:order_tickets,
     from(ot in OrderTicket,
       join: t in assoc(ot, :ticket),
       where: t.event_id == ^event_id,
       preload: ^merged
     )}
  end
end
