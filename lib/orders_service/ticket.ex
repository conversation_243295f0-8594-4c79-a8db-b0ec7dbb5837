defmodule OrdersService.Ticket do
  @moduledoc """
  Ticket schema and changeset
  """

  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Ecto.Multi
  alias Ecto.UUID
  alias ExServiceClient.Services.EventsService.Variant
  alias OrdersService.Bill
  alias OrdersService.InvitationOrder
  alias OrdersService.Order
  alias OrdersService.OrderTicket
  alias OrdersService.PersonalInformation
  alias OrdersService.Refunds.RefundTransaction
  alias OrdersService.Repo
  alias OrdersService.Ticket
  alias OrdersService.TicketGroup
  alias OrdersService.TicketHistory
  alias OrdersService.Tickets.ScanCode
  alias OrdersService.Tickets.Ticket, as: TicketCTX
  alias OrdersService.Tickets.TicketsQuota
  alias OrdersService.TicketSwap.SwapTransaction
  alias OrdersService.Workers.SeatsReleaseWorker

  require Logger

  @ticket_status [
    :ACTIVE,
    :CREATED,
    :DEFRAUDED,
    :FAILED,
    :INVITATION_REJECTED,
    :PENDING,
    :REFUNDED,
    :REFUNDING,
    :SWAPPED,
    :TIMEDOUT,
    :USED,
    :UNUSED
  ]

  @ticket_type [:DIGITAL, :HARD]

  @distribution_types [:GUEST_LIST_INVITATION, :SALES_CHANNEL, :REGULAR]

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  # styler:sort
  schema "tickets" do
    belongs_to(:attendee, PersonalInformation, foreign_key: :attendee_id)
    belongs_to(:owner, PersonalInformation, foreign_key: :owner_id)
    belongs_to(:ticket_group, TicketGroup, foreign_key: :ticket_group_id)
    field(:admission, :boolean, default: true)
    field(:category_id, :binary_id)
    field(:category_name, :string, virtual: true)
    field(:channel_config_id, :binary_id)
    field(:distribution_type, Ecto.Enum, values: @distribution_types, default: :REGULAR)
    field(:distribution_type_id, :binary_id)
    field(:distribution_type_info, :map, virtual: true)
    field(:event_document_id, :string)
    field(:event_id, :binary_id)
    field(:is_checkin_allowed, :boolean, default: false)
    field(:purchase_date, :utc_datetime)
    field(:seat, :string)
    field(:short_uuid, :string)
    field(:status, Ecto.Enum, values: @ticket_status)
    field(:type, Ecto.Enum, values: @ticket_type)
    field(:variant, :map, virtual: true)
    field(:variant_id, :binary_id)
    field(:voucher_document_id, :string)
    field(:voucher_id, :binary_id)
    has_many(:refund_transactions, RefundTransaction, foreign_key: :refund_item_id)
    has_many(:scan_codes, ScanCode, foreign_key: :ticket_id)
    has_many(:ticket_history, TicketHistory, foreign_key: :ticket_id)
    has_one(:order_ticket, OrderTicket)

    has_one(:swap_transaction, SwapTransaction, references: :id, foreign_key: :new_ticket_id)
    timestamps(updated_at: false)
  end

  # styler:sort
  @type t ::
          %Ticket{
            __meta__: Ecto.Schema.Metadata.t(),
            admission: boolean(),
            attendee: struct() | nil,
            attendee_id: binary() | nil,
            category_id: binary(),
            category_name: String.t(),
            channel_config_id: binary(),
            distribution_type: atom(),
            distribution_type_id: binary() | nil,
            distribution_type_info: map() | nil,
            event_document_id: String.t(),
            event_id: binary(),
            id: binary(),
            inserted_at: DateTime.t(),
            is_checkin_allowed: boolean(),
            order_ticket: struct(),
            owner: struct() | nil,
            owner_id: binary() | nil,
            purchase_date: DateTime.t(),
            refund_transactions: list(),
            scan_codes: list() | Ecto.Association.NotLoaded.t(),
            seat: String.t(),
            status: atom(),
            ticket_group: struct(),
            ticket_group_id: binary(),
            ticket_history: list(),
            variant: Variant.t() | nil,
            variant_id: binary(),
            voucher_document_id: String.t(),
            voucher_id: binary()
          }

  def changeset(ticket, attrs) do
    ticket
    |> cast(attrs, [
      :event_id,
      :event_document_id,
      :variant_id,
      :category_id,
      :is_checkin_allowed,
      :purchase_date,
      :owner_id,
      :attendee_id,
      :voucher_id,
      :voucher_document_id,
      :seat,
      :ticket_group_id,
      :channel_config_id,
      :status,
      :type,
      :admission,
      :inserted_at,
      :distribution_type,
      :distribution_type_id
    ])
    |> validate_required([
      :variant_id
    ])
    |> validate_inclusion(:status, @ticket_status)
    |> validate_inclusion(:type, @ticket_type)
    |> validate_inclusion(:distribution_type, @distribution_types)
    |> maybe_generate_ids()
  end

  @spec update_ticket(ticket :: Ticket.t(), attrs :: map()) :: {:ok, Ticket.t()} | {:error, any()}
  def update_ticket(ticket, attrs) do
    ticket
    |> Ticket.changeset(attrs)
    |> Repo.update()
  end

  def get_amount_tickets_sold_by_order_status(event_id, variant_id, order_status) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        inner_join: o in Order,
        on: ot.order_id == o.id,
        as: :order,
        where: o.status in ^order_status,
        where: t.variant_id == ^variant_id,
        where: t.status not in [:REFUNDED],
        where: t.admission == ^true,
        where: t.distribution_type != :GUEST_LIST_INVITATION
      )

    query
    |> filter_for_event_id(event_id)
    |> Repo.aggregate(:count, :id)
  end

  @spec get_maximum_number_of_attendees(event_id :: Ecto.UUID.t()) :: {integer()}
  def get_maximum_number_of_attendees(event_id) do
    query =
      from(t in Ticket,
        as: :ticket,
        where: t.status in [:ACTIVE, :USED, :UNUSED],
        where: t.distribution_type not in [:GUEST_LIST_INVITATION],
        where: t.admission == ^true
      )

    query
    |> filter_for_event_id(event_id)
    |> Repo.aggregate(:count, :id)
  end

  def get_total_amount_extras_sold_or_pending(event_id) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        inner_join: o in Order,
        on: ot.order_id == o.id,
        as: :order,
        where: o.status in [:PENDING, :PAID, :REFUND_PENDING],
        where: t.status not in [:REFUNDED],
        where: t.distribution_type != :GUEST_LIST_INVITATION,
        where: t.admission == ^false
      )

    query
    |> filter_for_event_id(event_id)
    |> Repo.aggregate(:count, :id)
  end

  def get_sold_or_pending_with_userinfo_by_event_id(event_id) do
    get_all_sold_or_pending_query()
    |> filter_for_event_id(event_id)
    |> Repo.all()
  end

  def get_sold_or_pending_with_userinfo_by_variant_id(variant_id) do
    get_all_sold_or_pending_query()
    |> filter_for_variant_id(variant_id)
    |> Repo.all()
  end

  def get_by_invitation_id(invitation_id) do
    get_tickets_with_invitation_order_query()
    |> filter_for_invitation_id(invitation_id)
    |> Repo.all()
  end

  def get_all_by_order_id(order_id, preloads \\ []) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        where: ot.order_id == ^order_id,
        preload: ^preloads
      )

    Repo.all(query)
  end

  @spec get_by_ids([Ecto.UUID.t()], [atom()]) :: [Ticket.t()]
  def get_by_ids(ticket_ids, preloads \\ []) do
    query =
      from(t in Ticket,
        where: t.id in ^ticket_ids,
        preload: ^preloads
      )

    Repo.all(query)
  end

  @spec get_all_by_order_id_and_category_ids_and_status(
          Ecto.UUID.t(),
          [Ecto.UUID.t()],
          [atom()],
          [atom()]
        ) ::
          [Order.t()]
  def get_all_by_order_id_and_category_ids_and_status(order_id, category_ids, status, preloads \\ []) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        where: ot.order_id == ^order_id,
        where: t.category_id in ^category_ids,
        where: t.status in ^status,
        preload: ^preloads
      )

    Repo.all(query)
  end

  @spec get_all_by_ids_category_ids_and_status([Ecto.UUID.t()], [Ecto.UUID.t()], [atom()], [
          atom()
        ]) ::
          [Ticket.t()]
  def get_all_by_ids_category_ids_and_status(ticket_ids, category_ids, status, preloads \\ []) do
    query =
      from(t in Ticket,
        as: :ticket,
        where: t.id in ^ticket_ids,
        where: t.category_id in ^category_ids,
        where: t.status in ^status,
        preload: ^preloads
      )

    Repo.all(query)
  end

  def get_unique_seat_labels_by_order_id(order_id) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        left_join: tg in assoc(t, :ticket_group),
        on: t.ticket_group_id == tg.id,
        as: :ticket_group,
        where: ot.order_id == ^order_id,
        where: not is_nil(t.seat) or not is_nil(t.ticket_group_id),
        select: %{
          event_id: t.event_id,
          event_document_id: t.event_document_id,
          label: coalesce(tg.label, t.seat)
        },
        distinct: coalesce(tg.label, t.seat)
      )

    Repo.all(query)
  end

  def get_all_with_personal_information_by_order_id(order_id) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        left_join: pi in PersonalInformation,
        on: t.owner_id == pi.id,
        as: :owner,
        left_join: tpi in PersonalInformation,
        on: t.attendee_id == tpi.id,
        as: :attendee,
        where: ot.order_id == ^order_id,
        select: %{ticket: t, personal_information: pi, ticket_personal_information: tpi}
      )

    Repo.all(query)
  end

  def get_paginated_by_user_id(user_id, page_size, offset_id, event_id, status, order_by) do
    user_id
    |> get_paginated_by_user_id(page_size, offset_id, order_by)
    |> filter_for_event_id(event_id)
    |> filter_for_ticket_status(status)
    |> Repo.all()
  end

  defp get_paginated_by_user_id(user_id, page_size, nil, order_by) do
    order_by = List.flatten([order_by | [asc: :id]])

    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        inner_join: o in Order,
        on: ot.order_id == o.id,
        as: :order,
        inner_join: pi in PersonalInformation,
        on: o.created_by == pi.id,
        as: :owner,
        where: t.status not in [:REFUNDED, :SWAPPED],
        where: o.status in [:PAID],
        preload: [:scan_codes, ticket_history: ^from(th in TicketHistory, order_by: [asc: th.inserted_at])],
        where: t.admission == ^true,
        order_by: ^order_by,
        limit: ^page_size
      )

    case UUID.dump(user_id) do
      {:ok, _} -> where(query, [_t, _ot, _o, pi], pi.user_id == ^user_id)
      _ -> where(query, [_t, _ot, _o, pi], pi.user_document_id == ^user_id)
    end
  end

  defp get_paginated_by_user_id(user_id, page_size, offset_id, order_by) do
    order_by = List.flatten([order_by | [asc: :id]])

    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        inner_join: o in Order,
        on: ot.order_id == o.id,
        as: :order,
        inner_join: pi in PersonalInformation,
        on: o.created_by == pi.id,
        as: :owner,
        where: t.status not in [:REFUNDED, :SWAPPED],
        where: o.status in [:PAID],
        where: t.id > ^offset_id,
        preload: :ticket_history,
        order_by: ^order_by,
        limit: ^page_size
      )

    case UUID.dump(user_id) do
      {:ok, _} -> where(query, [_t, _ot, _o, pi], pi.user_id == ^user_id)
      _ -> where(query, [_t, _ot, _o, pi], pi.user_document_id == ^user_id)
    end
  end

  def get_all_with_bill_by_order_id(order_id) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        inner_join: b in Bill,
        on: ot.bill_id == b.id,
        as: :bill,
        where: ot.order_id == ^order_id,
        select: %{ticket: t, bill: b}
      )

    Repo.all(query)
  end

  def get_all_by_user_id(user_id) when is_binary(user_id) do
    query = from(t in Ticket, where: t.user_id == ^user_id)

    Repo.all(query)
  end

  # TODO: Remove after accounts-service migration
  # SD1-1594
  def get_all_by_user_id(user_id) do
    query = from(t in Ticket, where: t.user_document_id == ^user_id)

    Repo.all(query)
  end

  @spec get(ticket_id :: Ecto.UUID.t(), preloads :: list()) :: Ticket.t() | nil
  def get(ticket_id, preloads \\ [:order_ticket, :ticket_history]) do
    Repo.one(from(t in Ticket, preload: ^preloads, where: t.id == ^ticket_id))
  end

  @spec get_by_scan_code(scan_code :: String.t(), preloads :: list()) ::
          Ticket.t() | nil
  def get_by_scan_code(scan_code, preloads \\ []) do
    Repo.one(
      from(t in Ticket,
        inner_join: sc in ScanCode,
        on: sc.ticket_id == t.id,
        preload: ^preloads,
        where: sc.scan_code == ^scan_code
      )
    )
  end

  @spec get_ticket_for_refund_finalization(ticket_id :: Ecto.UUID.t() | any()) :: Ticket.t() | nil
  def get_ticket_for_refund_finalization(ticket_id) do
    case Ecto.UUID.dump(ticket_id) do
      {:ok, _binary_id} -> get(ticket_id)
      _ -> nil
    end
  end

  def get_include_invitation_order(ticket_id) do
    order_query = from(o in Order, preload: [:invitation_orders])

    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        inner_join: o in Order,
        on: ot.order_id == o.id,
        as: :order,
        left_join: io in InvitationOrder,
        on: o.id == io.order_id,
        as: :invitation_order,
        where: t.id == ^ticket_id,
        preload: [order_ticket: [order: ^order_query]]
      )

    Repo.one(query)
  end

  def update(ticket, attrs) do
    ticket
    |> Ticket.changeset(attrs)
    |> Repo.update()
  end

  @spec set_status(
          Ticket.t(),
          atom(),
          binary() | nil,
          Ecto.UUID.t() | nil,
          atom() | nil,
          boolean(),
          map()
        ) ::
          {:ok, Ticket.t()} | {:error, any()}
  def set_status(
        %{status: current_status, id: ticket_id} = ticket,
        :REFUNDED,
        user_id,
        _location_id,
        _location_type,
        update_counter,
        ticket_history_metadata
      ) do
    Logger.debug("Change ticket status for ticket #{inspect(ticket_id)} from #{inspect(current_status)} to :REFUNDED")

    with {:status_change, true} <-
           {:status_change, status_change_allowed?(current_status, :REFUNDED)},
         {:ok, updated_ticket} <-
           ticket
           |> Ticket.changeset(%{status: :REFUNDED, refunded_at: DateTime.utc_now()})
           |> Repo.update() do
      TicketHistory.create(ticket, :REFUNDED, user_id, nil, nil, ticket_history_metadata)

      maybe_queue_seat_release(ticket)

      if update_counter do
        TicketCTX.update_tickets_counter(updated_ticket)
      end

      {:ok, updated_ticket}
    else
      {:status_change, _} ->
        Logger.error(
          "Can't change ticket status for ticket #{inspect(ticket_id)} from #{inspect(current_status)} to :REFUNDED because it's a forbidden change."
        )

        {:error, :forbidden_status_change}

      {:error, error} ->
        Logger.error(
          "Can't change ticket status for ticket #{inspect(ticket_id)} from #{inspect(current_status)} to :REFUNDED, because of #{inspect(error)}"
        )

        {:error, error}

      error ->
        Logger.error(
          "Can't change ticket status for ticket #{inspect(ticket_id)} from #{inspect(current_status)} to :REFUNDED, because of #{inspect(error)}"
        )

        {:error, error}
    end
  end

  def set_status(
        %{status: current_status, id: ticket_id} = ticket,
        status,
        user_id,
        location_id,
        location_type,
        update_counter,
        ticket_history_metadata
      ) do
    Logger.debug(
      "Change ticket status for ticket #{inspect(ticket_id)} from #{inspect(current_status)} to #{inspect(status)}"
    )

    with {:status_change, true} <-
           {:status_change, status_change_allowed?(current_status, status)},
         {:ok, updated_ticket} <-
           ticket
           |> Ticket.changeset(%{status: status})
           |> Repo.update() do
      TicketHistory.create(
        ticket,
        status,
        user_id,
        location_id,
        location_type,
        ticket_history_metadata
      )

      if update_counter do
        TicketCTX.update_tickets_counter(updated_ticket)
      end

      {:ok, updated_ticket}
    else
      {:status_change, _} ->
        Logger.error(
          "Can't change ticket status for ticket #{inspect(ticket_id)} from #{inspect(current_status)} to #{inspect(status)} because it's a forbidden change."
        )

        {:error, :forbidden_status_change}

      {:error, error} ->
        Logger.error(
          "Can't change ticket status for ticket #{inspect(ticket_id)} from #{inspect(current_status)} to #{inspect(status)}, because of #{inspect(error)}"
        )

        {:error, error}

      error ->
        Logger.error(
          "Can't change ticket status for ticket #{inspect(ticket_id)} from #{inspect(current_status)} to #{inspect(status)}, because of #{inspect(error)}"
        )

        {:error, error}
    end
  end

  @spec reset_status(event_id :: Ecto.UUID.t(), category_id :: Ecto.UUID.t()) ::
          {:ok, map()} | {:error, any()}
  def reset_status(event_id, category_id) do
    status = :ACTIVE

    tickets =
      Repo.all(
        from(t in Ticket,
          as: :ticket,
          where: t.event_id == ^event_id,
          where: t.category_id == ^category_id,
          where: t.status == :USED or t.status == :UNUSED
        )
      )

    Multi.new()
    |> reset_status_for_tickets(tickets)
    |> create_ticket_histories_for_tickets(tickets, status)
    |> Repo.transaction()
  end

  @spec set_status_by_order_id(
          order_id :: Ecto.UUID.t() | nil,
          status :: atom(),
          user_id :: String.t() | nil
        ) :: :ok | {:error, any()}
  def set_status_by_order_id(nil, status, _user_id) do
    Logger.error("Can't set status for all tickets in order to status #{inspect(status)} because order_id is nil")

    {:error, :unknown_order_id}
  end

  def set_status_by_order_id(order_id, status, user_id) do
    Logger.debug("Set status for all tickets in order #{inspect(order_id)} to status #{inspect(status)}")

    tickets = get_all_by_order_id(order_id)

    response =
      Repo.transaction(fn ->
        Enum.map(tickets, fn ticket ->
          set_status(ticket, status, user_id, nil, nil, true, %{})
        end)
      end)

    case response do
      {:ok, _response} ->
        :ok

      {:error, error} ->
        Logger.error(
          "Can't set status for all tickets in order #{inspect(order_id)} to status #{inspect(status)} because of #{inspect(error)}"
        )

        {:error, error}
    end
  end

  @spec get_tickets_by_id(tickets_ids :: [Ecto.UUID.t()]) ::
          {:ok, [Ticket.t()]} | {:error, list()}
  def get_tickets_by_id(tickets_ids) do
    preloads = [:ticket_history, order_ticket: [:order]]

    tickets_ids
    |> Enum.reduce_while({[], []}, fn ticket_id, {errors, tickets} ->
      case get(ticket_id, preloads) do
        nil -> {:halt, {["Ticket with id #{inspect(ticket_id)} not found" | errors], tickets}}
        ticket -> {:cont, {errors, [ticket | tickets]}}
      end
    end)
    |> case do
      {[], tickets} -> {:ok, tickets}
      {errors, _} -> {:error, errors}
    end
  end

  def mark_as_fraud(%{order_ticket: %{order_id: order_id}} = ticket, user_id) do
    update_res =
      Repo.transaction(fn ->
        Order.set_status(Order.get(order_id), :DEFRAUDED, user_id)

        Enum.reduce_while(get_all_by_order_id(order_id), :ok, fn t, _acc ->
          update_ticket_res =
            set_status(t, :DEFRAUDED, user_id, nil, nil, true, %{})

          case update_ticket_res do
            {:ok, _ticket} -> {:cont, :ok}
            {:error, error} -> {:halt, {:error, error}}
          end
        end)
      end)

    case update_res do
      {:ok, _} ->
        Logger.debug("Mark ticket #{inspect(ticket.id)} as defrauded.")
        {:ok, get(ticket.id)}

      {:error, error} ->
        {:error, error}

      error ->
        {:error, error}
    end
  end

  # SD1-3088: Re-added after removal in https://github.com/stagedates/orders-service/pull/168 due to the API still being in use.
  # Remove this code once the new API is being used
  def refund(ticket), do: refund(ticket, nil)

  def refund(%{order_ticket: %{order_id: order_id}} = ticket, user_id) do
    update_ticket_res =
      set_status(ticket, :REFUNDED, user_id, nil, nil, true, %{})

    case update_ticket_res do
      {:ok, updated_ticket} ->
        not_refunded_tickets_for_order_count =
          order_id
          |> get_all_by_order_id()
          |> Enum.count(fn t -> t.status != :REFUNDED end)

        if not_refunded_tickets_for_order_count == 0,
          do: Order.set_status(Order.get(order_id), :REFUNDED)

        {:ok, updated_ticket}

      {:error, error} ->
        Logger.error("Can't refund ticket #{inspect(ticket.id)} because of #{inspect(error)}")
        {:error, error}
    end
  end

  def refund(ticket, user_id), do: set_status(ticket, :REFUNDED, user_id, nil, nil, true, %{})

  # add user_id as parameter to prepare useage in backoffice-tool
  def finalize_tickets_for_event(event_id, user_id \\ nil) do
    Logger.debug(
      "Finalize all tickets for event #{inspect(event_id)} and set status to 'UNUSED' for 'ACTIVE' tickets by user #{inspect(user_id)}"
    )

    tickets =
      get_all_active_tickets_query()
      |> filter_for_event_id(event_id)
      |> Repo.all()

    update_res =
      Repo.transaction(
        fn ->
          try do
            Enum.reduce_while(tickets, :ok, fn t, _acc ->
              update_ticket_res =
                set_status(t, :UNUSED, user_id, nil, nil, false, %{})

              case update_ticket_res do
                {:ok, _ticket} -> {:cont, :ok}
                {:error, error} -> {:halt, {:error, error}}
              end
            end)
          rescue
            e in Postgrex.Error ->
              case e.postgres.code do
                "57014" ->
                  {:error, :postgres_timeout}

                error_code ->
                  Logger.error(
                    "Can't finzlize tickets for event #{inspect(event_id)} becaus of postgres_error #{inspect(error_code)}"
                  )

                  {:error, :unknown_postgres_error}
              end
          end
        end,
        timeout: 60_000
      )

    case update_res do
      {:ok, _} ->
        Logger.debug("Mark all 'ACTIVE' tickets as 'UNUSED' for event #{inspect(event_id)}")
        TicketCTX.update_tickets_counter(tickets)

        :ok

      {:error, error} ->
        Logger.error(
          "Can't finalize the tickets for event #{inspect(event_id)} as defrauded because of #{inspect(error)}"
        )

        {:error, error}

      error ->
        Logger.error(
          "Can't finalize the tickets for event #{inspect(event_id)} as defrauded because of #{inspect(error)}"
        )

        {:error, error}
    end
  end

  def set_invitation_rejected_by_invitation_id(invitation_id, user_id) do
    tickets = get_all_by_invitation_id(invitation_id)

    update_res =
      Repo.transaction(fn ->
        Enum.reduce_while(tickets, :ok, fn t, _acc ->
          update_ticket_res =
            set_status(t, :INVITATION_REJECTED, user_id, nil, nil, true, %{})

          case update_ticket_res do
            {:ok, _ticket} -> {:cont, :ok}
            {:error, error} -> {:halt, {:error, error}}
          end
        end)
      end)

    case update_res do
      {:ok, _} ->
        Logger.debug("Rejected invitation #{inspect(invitation_id)} for all claimed tickets")
        :ok

      {:error, error} ->
        Logger.error("Can't reject invitation #{inspect(invitation_id)} for tickets because of #{inspect(error)}")

        {:error, error}

      error ->
        Logger.error("Can't reject invitation #{inspect(invitation_id)} for tickets because of #{inspect(error)}")

        {:error, error}
    end
  end

  def get_all_by_invitation_id(invitation_id) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        inner_join: io in InvitationOrder,
        on: io.order_id == ot.order_id,
        as: :invitation_order,
        where: io.invitation_id == ^invitation_id
      )

    Repo.all(query)
  end

  def get_total_amount_checked_in_by_event_id(event_id) do
    query =
      from(t in Ticket,
        as: :ticket,
        where: t.status in [:USED],
        where: t.distribution_type not in [:GUEST_LIST_INVITATION],
        where: t.admission == ^true
      )

    query
    |> filter_for_event_id(event_id)
    |> Repo.aggregate(:count, :id)
  end

  def get_redeemed_extras_by_event_id(event_id) do
    query =
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        where: t.status == :USED,
        where: t.admission == ^false,
        where: t.distribution_type != :GUEST_LIST_INVITATION
      )

    query
    |> filter_for_event_id(event_id)
    |> Repo.aggregate(:count)
  end

  def calculate_organizer_total(%{filter_type: field, filter_value: value}) do
    join_order_ticket_query()
    |> join(:inner, [order_ticket: ot], b in Bill, on: ot.bill_id == b.id, as: :bill)
    |> filter_for_order_status([:PAID, :PENDING, :REFUND_PENDING])
    |> filter_for_ticket_status([:ACTIVE, :USED, :UNUSED, :PENDING, :SWAPPED])
    |> filter_ticket_by_field(field, value)
    |> group_by([order: o], o.status)
    |> select(
      [bill: b, order: o],
      %{
        order_status: o.status,
        organizer_total:
          sum(
            coalesce(b.promoter_total, 0) +
              coalesce(b.promoter_kickback, 0) +
              coalesce(b.promoter_kickback_tax, 0)
          )
      }
    )
    |> Repo.all()
  end

  def search_by_event_id(event_id, query, checked_in, page, page_size) do
    query =
      from(t in Ticket,
        as: :ticket,
        left_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        left_join: o in Order,
        on: ot.order_id == o.id,
        as: :order,
        left_join: pi in PersonalInformation,
        on: o.created_by == pi.id,
        as: :owner,
        left_join: tpi in PersonalInformation,
        on: t.attendee_id == tpi.id,
        as: :attendee,
        where: ^build_attendee_search_conditions(query),
        where: t.status not in [:REFUNDED, :SWAPPED],
        where: o.status == :PAID or is_nil(o.id),
        where: t.event_id == ^event_id,
        preload: [:ticket_history, :scan_codes],
        select: %{
          ticket: t,
          personal_information: pi,
          ticket_personal_information: tpi,
          order: o
        }
      )

    query =
      case checked_in do
        "true" -> where(query, [ticket: t], t.status == :USED)
        "false" -> where(query, [ticket: t], t.status in [:ACTIVE, :UNUSED])
        _ -> query
      end

    Repo.paginate(query, page: page, page_size: page_size)
  end

  @spec get_by_event_id(
          event_id :: Ecto.UUID.t(),
          admission :: boolean(),
          page :: integer() | nil,
          page_size :: integer() | nil,
          preloads :: [:atom]
        ) ::
          [Ticket.t()] | Paginator.Page.t() | []
  def get_by_event_id(event_id, admission, page, page_size, preloads \\ []) do
    query =
      filter_for_admission(
        from(t in Ticket,
          as: :ticket,
          left_join: ot in OrderTicket,
          on: t.id == ot.ticket_id,
          as: :order_ticket,
          left_join: o in Order,
          on: ot.order_id == o.id,
          as: :order,
          where: t.event_id == ^event_id,
          where: t.status not in [:REFUNDED],
          where: o.status == :PAID or is_nil(o.id),
          preload: ^preloads
        ),
        admission
      )

    if(is_nil(page) && is_nil(page_size)) do
      Repo.all(query)
    else
      Repo.paginate(query, page: page, page_size: page_size)
    end
  end

  @spec count_sold_or_pending_for_event_id(
          event_id :: Ecto.UUID.t(),
          admission :: boolean() | nil
        ) ::
          integer()
  def count_sold_or_pending_for_event_id(event_id, admission \\ nil) do
    count_sold_or_pending_query()
    |> filter_for_event_id(event_id)
    |> filter_for_admission(admission)
    |> Repo.one()
  end

  @spec count_sold_or_pending_for_ticket_category_id(ticket_category_id :: Ecto.UUID.t()) ::
          integer()
  def count_sold_or_pending_for_ticket_category_id(ticket_category_id) do
    count_sold_or_pending_query()
    |> filter_for_ticket_category_id(ticket_category_id)
    |> Repo.one()
  end

  @spec count_sold_or_pending_for_variant_id(
          variant_id :: Ecto.UUID.t(),
          admission :: boolean() | nil
        ) :: integer()
  def count_sold_or_pending_for_variant_id(variant_id, admission \\ nil) do
    count_sold_or_pending_query()
    |> filter_for_variant_id(variant_id)
    |> filter_for_admission(admission)
    |> Repo.one()
  end

  @spec count_sold_or_pending_for_voucher_id(voucher_id :: Ecto.UUID.t()) :: integer() | nil
  def count_sold_or_pending_for_voucher_id(voucher_id) do
    count_sold_or_pending_query()
    |> filter_for_voucher_id(voucher_id)
    |> Repo.one()
  end

  @spec calculate_total_sum_for_event_id(event_id :: Ecto.UUID.t()) :: integer() | nil
  def calculate_total_sum_for_event_id(event_id) do
    calculate_sold_or_pending_query()
    |> filter_for_event_id(event_id)
    |> Repo.one()
  end

  @spec calculate_total_sum_for_variant_id(variant_id :: Ecto.UUID.t()) :: integer() | nil
  def calculate_total_sum_for_variant_id(variant_id) do
    calculate_sold_or_pending_query()
    |> filter_for_variant_id(variant_id)
    |> Repo.one()
  end

  @spec calculate_total_sum_for_ticket_category_id(ticket_category_id :: Ecto.UUID.t()) ::
          integer() | nil
  def calculate_total_sum_for_ticket_category_id(ticket_category_id) do
    calculate_sold_or_pending_query()
    |> filter_for_ticket_category_id(ticket_category_id)
    |> Repo.one()
  end

  @spec calculate_total_sum_for_event_ids(event_ids :: [Ecto.UUID.t()]) :: [
          %{id: Ecto.UUID.t(), sum: integer()}
        ]
  def calculate_total_sum_for_event_ids(event_ids) do
    event_ids
    |> calculate_sold_query_multiple_events()
    |> Repo.all()
  end

  @spec count_total_checked_in_for_event_ids([Ecto.UUID.t()]) :: [
          %{id: Ecto.UUID.t(), count: integer}
        ]
  def count_total_checked_in_for_event_ids(event_ids) do
    event_ids
    |> count_used_query_for_event(true)
    |> group_by([ticket: t], t.event_id)
    |> select([ticket: t], %{id: t.event_id, count: count(t.id)})
    |> Repo.all()
  end

  @spec count_total_checked_in_for_event_id(event_id :: Ecto.UUID.t()) :: integer() | nil
  def count_total_checked_in_for_event_id(event_id) do
    event_id
    |> count_used_query_for_event(true)
    |> select([ticket: t], count(t.id))
    |> Repo.one()
  end

  @spec calculate_total_sum_for_variants_ids(variant_ids :: [Ecto.UUID.t()]) :: [
          %{id: Ecto.UUID.t(), sum: integer()}
        ]
  def calculate_total_sum_for_variants_ids(variant_ids) do
    variant_ids
    |> count_sold_query_multiple_variants()
    |> Repo.all()
  end

  @spec calculate_total_sum_for_vouchers_ids([Ecto.UUID.t()]) :: [
          %{id: Ecto.UUID.t(), sum: integer()}
        ]
  def calculate_total_sum_for_vouchers_ids(voucher_ids) do
    voucher_ids
    |> count_sold_or_pending_voucher_query()
    |> Repo.all()
  end

  def get_order_owners(order_id) do
    Repo.all(
      from(t in Ticket,
        as: :ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        inner_join: o in Order,
        on: ot.order_id == o.id,
        as: :order,
        inner_join: p in PersonalInformation,
        on: p.id == t.owner_id,
        as: :owner,
        where: ot.order_id == ^order_id,
        group_by: [t.owner_id, t.event_id, p.user_id, p.user_document_id, o.status],
        select: %{
          event_id: t.event_id,
          user_id: p.user_id,
          user_document_id: p.user_document_id,
          status: o.status
        }
      )
    )
  end

  @doc """
  This function is only going to be used for back filling the conversation participants
  hence no need to filter by status as there  will be no further iterations
  """
  def get_order_owners_by_event_id(event_id) do
    Repo.all(
      from(t in Ticket,
        inner_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        inner_join: o in Order,
        on: ot.order_id == o.id,
        inner_join: p in PersonalInformation,
        on: p.id == t.owner_id,
        where: t.event_id == ^event_id,
        where: o.status in [:PENDING, :PAID, :REFUND_PENDING],
        group_by: [t.event_id, p.user_id, p.user_document_id],
        select: %{event_id: t.event_id, user_id: p.user_id, user_document_id: p.user_document_id}
      )
    )
  end

  # FIXME: remove together with show_for_user fix
  @spec find_date_for_status(Ticket.t(), atom()) :: DateTime.t() | nil
  def find_date_for_status(%Ticket{status: :ACTIVE}, :USED), do: nil
  def find_date_for_status(%Ticket{status: :UNUSED}, :USED), do: nil

  def find_date_for_status(%Ticket{ticket_history: ticket_history}, status) when is_list(ticket_history) do
    Enum.find_value(ticket_history, fn history ->
      if history.status_after == status, do: history.inserted_at
    end)
  end

  def find_date_for_status(_ticket, _status), do: nil

  @spec count_not_refunded_tickets_for_order_by_order_id(order_id :: Ecto.UUID.t()) :: integer()
  def count_not_refunded_tickets_for_order_by_order_id(order_id) do
    order_id
    |> get_all_by_order_id()
    |> Enum.count(fn %{status: status} -> status != :REFUNDED end)
  end

  @spec maybe_set_order_as_refunded(integer(), Ecto.UUID.t()) :: :ok | {:error, any()}
  def maybe_set_order_as_refunded(0, order_id) do
    order_id
    |> Order.get()
    |> Order.set_status(:REFUNDED)
    |> case do
      {:ok, %Order{} = _order} -> :ok
      error -> {:error, error}
    end
  end

  def maybe_set_order_as_refunded(_, _order_id), do: :ok

  @spec maybe_revert_order_status(order :: Order.t()) :: :ok | {:error, any()}
  def maybe_revert_order_status(%Order{status: :REFUNDED} = order) do
    case Order.set_status(order, :PAID) do
      {:ok, %Order{} = _order} -> :ok
      error -> {:error, error}
    end
  end

  def maybe_revert_order_status(_order), do: :ok

  @spec status_change_allowed?(atom(), atom()) :: boolean()
  def status_change_allowed?(_current_status, :DEFRAUDED), do: true
  def status_change_allowed?(_current_status, :INVITATION_REJECTED), do: true
  def status_change_allowed?(:PENDING, :ACTIVE), do: true
  def status_change_allowed?(:CREATED, :ACTIVE), do: true
  def status_change_allowed?(:PENDING, :TIMEDOUT), do: true
  def status_change_allowed?(:CREATED, :TIMEDOUT), do: true
  def status_change_allowed?(:PENDING, :FAILED), do: true
  def status_change_allowed?(:CREATED, :FAILED), do: true
  def status_change_allowed?(:ACTIVE, :USED), do: true
  def status_change_allowed?(:ACTIVE, :UNUSED), do: true
  def status_change_allowed?(:ACTIVE, :REFUNDING), do: true
  def status_change_allowed?(:ACTIVE, :REFUNDED), do: true
  def status_change_allowed?(:REFUNDING, :ACTIVE), do: true
  def status_change_allowed?(:REFUNDING, :REFUNDED), do: true
  def status_change_allowed?(:ACTIVE, :SWAPPED), do: true
  def status_change_allowed?(_current_status, _new_status), do: false

  @spec build_delete_all_multi(Multi.t(), event_id :: UUID.t()) :: Multi.t()
  def build_delete_all_multi(multi, event_id) do
    Multi.delete_all(
      multi,
      :tickets,
      from(t in __MODULE__, where: t.event_id == ^event_id, select: %{ticket_ids: t.id})
    )
  end

  defp reset_status_for_tickets(multi, tickets) do
    Enum.reduce(tickets, multi, fn ticket, multi ->
      Multi.update(multi, {:ticket, ticket.id}, Ticket.changeset(ticket, %{status: :ACTIVE}))
    end)
  end

  defp create_ticket_histories_for_tickets(multi, tickets, status) do
    Enum.reduce(tickets, multi, fn ticket, multi ->
      Multi.run(
        multi,
        {:ticket_history, ticket.id},
        fn _repo, _ -> TicketHistory.create(ticket, status, nil) end
      )
    end)
  end

  def count_existing_items_for_variant_id(variant_id, admission \\ nil) do
    count_existing_query()
    |> filter_for_variant_id(variant_id)
    |> filter_for_admission(admission)
    |> Repo.one() || 0
  end

  def count_existing_items_for_ticket_category_id(ticket_category_id, admission \\ nil, reserved? \\ false) do
    count_existing_query()
    |> filter_for_ticket_category_id(ticket_category_id)
    |> filter_for_admission(admission)
    |> filter_reserved_distribution_types(reserved?)
    |> Repo.one() || 0
  end

  def count_existing_items_for_distribution_type(distribution_type_id, distribution_type, reserved? \\ false) do
    count_existing_query()
    |> filter_for_distribution_type(distribution_type_id, distribution_type)
    |> filter_reserved_distribution_types(reserved?)
    |> Repo.one() || 0
  end

  defp count_sold_or_pending_voucher_query(voucher_ids) do
    from(t in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      on: t.id == ot.ticket_id,
      as: :order_ticket,
      inner_join: o in Order,
      on: ot.order_id == o.id,
      as: :order,
      where: t.voucher_id in ^voucher_ids,
      where: o.status in [:PAID, :PENDING, :REFUND_PENDING],
      where: t.status not in [:REFUNDED],
      group_by: t.voucher_id,
      select: %{id: t.voucher_id, sum: count(t.voucher_id)}
    )
  end

  defp count_sold_or_pending_query do
    from(t in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      on: t.id == ot.ticket_id,
      as: :order_ticket,
      inner_join: o in Order,
      on: ot.order_id == o.id,
      as: :order,
      where: o.status in [:PAID, :PENDING, :REFUND_PENDING],
      where: t.status not in [:REFUNDED],
      where: t.distribution_type != :GUEST_LIST_INVITATION,
      select: count(t.id)
    )
  end

  defp join_order_ticket_query do
    from(t in Ticket,
      as: :ticket,
      left_join: ot in OrderTicket,
      on: t.id == ot.ticket_id,
      as: :order_ticket,
      left_join: o in Order,
      on: ot.order_id == o.id,
      as: :order
    )
  end

  defp count_existing_query do
    from(t in Ticket,
      as: :ticket,
      where:
        t.status not in [
          :INVITATION_REJECTED,
          :FAILED,
          :REFUNDING,
          :REFUNDED,
          :SWAPPED,
          :TIMEDOUT,
          :CREATED
        ],
      select: count(t.id)
    )
  end

  defp calculate_sold_or_pending_query do
    join_order_ticket_query()
    |> join(:inner, [order_ticket: ot], b in Bill, on: ot.bill_id == b.id, as: :bill)
    |> filter_for_order_status([:PAID, :PENDING, :REFUND_PENDING])
    |> filter_for_ticket_status([:ACTIVE, :USED, :UNUSED, :PENDING, :SWAPPED])
    |> group_by([ticket: t], t.event_id)
    |> select(
      [bill: b],
      sum(
        coalesce(b.promoter_total, 0) +
          coalesce(b.promoter_kickback, 0) +
          coalesce(b.promoter_kickback_tax, 0)
      )
    )
  end

  defp calculate_sold_query_multiple_events(event_ids) do
    join_order_ticket_query()
    |> join(:inner, [order_ticket: ot], b in Bill, on: ot.bill_id == b.id, as: :bill)
    |> where([ticket: t], t.distribution_type != :GUEST_LIST_INVITATION)
    |> filter_for_order_status(:PAID)
    |> filter_for_ticket_status([:ACTIVE, :USED, :UNUSED, :SWAPPED])
    |> filter_for_event_id(event_ids)
    |> group_by([ticket: t], t.event_id)
    |> select([ticket: t, bill: b], %{
      id: t.event_id,
      sum:
        sum(
          coalesce(b.promoter_total, 0) +
            coalesce(b.promoter_kickback, 0) +
            coalesce(b.promoter_kickback_tax, 0)
        )
    })
  end

  defp count_used_query_for_event(event_id, admission) do
    join_order_ticket_query()
    |> join(:left, [order_ticket: ot], b in Bill, on: ot.bill_id == b.id, as: :bill)
    |> where([ticket: t], t.distribution_type != :GUEST_LIST_INVITATION)
    |> filter_for_ticket_status(:USED)
    |> filter_for_event_id(event_id)
    |> filter_for_admission(admission)
  end

  defp count_sold_query_multiple_variants(variant_ids) do
    from(t in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      on: t.id == ot.ticket_id,
      as: :order_ticket,
      inner_join: o in Order,
      on: ot.order_id == o.id,
      as: :order,
      inner_join: b in Bill,
      on: ot.bill_id == b.id,
      where: t.variant_id in ^variant_ids,
      where: o.status in [:PAID, :PENDING, :REFUND_PENDING],
      where: t.status not in [:REFUNDED],
      where: t.distribution_type != :GUEST_LIST_INVITATION,
      group_by: t.variant_id,
      select: %{id: t.variant_id, sum: count(t.variant_id)}
    )
  end

  defp get_all_sold_or_pending_query do
    from(t in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      on: t.id == ot.ticket_id,
      as: :order_ticket,
      inner_join: o in Order,
      on: ot.order_id == o.id,
      as: :order,
      join: p_attendee in PersonalInformation,
      on: t.attendee_id == p_attendee.id,
      as: :attendee_personal_inforamtion,
      join: p_owner in PersonalInformation,
      on: t.owner_id == p_owner.id,
      as: :owner_personal_information,
      join: b in Bill,
      on: ot.bill_id == b.id,
      as: :bill,
      where: o.status in [:PENDING, :PAID, :REFUND_PENDING],
      where: t.status not in [:REFUNDED],
      where: t.distribution_type != :GUEST_LIST_INVITATION,
      preload: [:ticket_history],
      select: %{
        ticket: t,
        attendee: p_attendee,
        owner: p_owner,
        order: o,
        bill: b,
        order_ticket: ot
      }
    )
  end

  defp get_tickets_with_invitation_order_query do
    from(t in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      on: t.id == ot.ticket_id,
      as: :order_ticket,
      inner_join: io in InvitationOrder,
      on: ot.order_id == io.order_id,
      as: :invitation_order,
      select: %{ticket: t},
      preload: [:attendee, :ticket_history]
    )
  end

  defp get_all_active_tickets_query do
    from(t in Ticket,
      as: :ticket,
      where: t.status == :ACTIVE
    )
  end

  defp filter_ticket_by_field(query, field, value), do: where(query, [ticket: t], field(t, ^field) == ^value)

  defp filter_for_event_id(query, nil), do: query

  defp filter_for_event_id(query, event_id) when is_list(event_id),
    do: where(query, [ticket: t], t.event_id in ^event_id)

  defp filter_for_event_id(query, event_id) when is_binary(event_id),
    do: where(query, [ticket: t], t.event_id == ^event_id)

  defp filter_for_event_id(query, _event_id), do: query

  defp filter_for_voucher_id(query, nil), do: query

  defp filter_for_voucher_id(query, voucher_id) do
    where(query, [ticket: t], t.voucher_id == ^voucher_id)
  end

  defp filter_for_variant_id(query, nil), do: query

  defp filter_for_variant_id(query, variant_id) when is_list(variant_id),
    do: where(query, [ticket: t], t.variant_id in ^variant_id)

  defp filter_for_variant_id(query, variant_id) when is_binary(variant_id),
    do: where(query, [ticket: t], t.variant_id == ^variant_id)

  defp filter_for_variant_id(query, nil), do: query

  defp filter_for_invitation_id(query, nil), do: query

  defp filter_for_invitation_id(query, invitation_id) do
    where(
      query,
      [invitation_order: invitation_order],
      invitation_order.invitation_id == ^invitation_id
    )
  end

  defp filter_for_ticket_category_id(query, nil), do: query

  defp filter_for_ticket_category_id(query, category_id) do
    where(query, [ticket: t], t.category_id == ^category_id)
  end

  defp filter_for_ticket_status(query, nil), do: query

  defp filter_for_ticket_status(query, status) when is_list(status), do: where(query, [ticket: t], t.status in ^status)

  defp filter_for_ticket_status(query, status) when is_atom(status), do: where(query, [ticket: t], t.status == ^status)

  defp filter_for_ticket_status(query, _status), do: query

  defp filter_for_order_status(query, status) when is_list(status), do: where(query, [order: o], o.status in ^status)

  defp filter_for_order_status(query, status) when is_atom(status), do: where(query, [order: o], o.status == ^status)

  defp filter_for_order_status(query, _status), do: query

  defp filter_for_admission(query, nil), do: query

  defp filter_for_admission(query, admission) do
    where(query, [ticket: t], t.admission == ^admission)
  end

  defp filter_for_distribution_type(query, distribution_type_id, distribution_type),
    do:
      query
      |> where([ticket: t], t.distribution_type_id == ^distribution_type_id)
      |> where([ticket: t], t.distribution_type == ^distribution_type)

  defp filter_reserved_distribution_types(query, true),
    do: query |> ensure_distribution_type_ticket_quotas_is_joined() |> where([tickets_quota: tq], tq.reserved_quota > 0)

  defp filter_reserved_distribution_types(query, false), do: query

  defp maybe_queue_seat_release(%Ticket{seat: nil}), do: :ok

  defp maybe_queue_seat_release(%Ticket{id: ticket_id}) do
    case SeatsReleaseWorker.queue_tickets([ticket_id]) do
      {:ok, _} ->
        Logger.info("Queued seat release for ticket #{ticket_id}")
        :ok

      {:error, _} ->
        Logger.critical("Failed to queue seat release for ticket #{ticket_id}")
        {:error, :queue_seat_release_failed}
    end
  end

  defp ensure_distribution_type_ticket_quotas_is_joined(query) do
    # Reuse existing join if present, otherwise create new one
    case Enum.find(query.joins, &match?(%{source: {_, TicketsQuota}}, &1)) do
      nil ->
        join(query, :inner, [ticket: t], tq in TicketsQuota,
          as: :tickets_quota,
          on: t.distribution_type_id == tq.type_id
        )

      _existing_join ->
        query
    end
  end

  # credo:disable-for-lines:17 Credo.Check.Refactor.CyclomaticComplexity
  defp build_attendee_search_conditions(query_params) do
    search = "%#{query_params.query}%"
    given_name_search = "%#{query_params.given_name}%"
    family_name_search = "%#{query_params.family_name}%"

    dynamic(
      [owner: pi, attendee: tpi, order: o],
      ilike(pi.given_name, ^search) or
        ilike(pi.family_name, ^search) or
        ilike(tpi.given_name, ^search) or
        ilike(tpi.family_name, ^search) or
        ilike(o.email, ^search) or
        ilike(tpi.email, ^search) or
        (ilike(pi.given_name, ^given_name_search) and ilike(pi.family_name, ^family_name_search)) or
        (ilike(tpi.given_name, ^given_name_search) and ilike(tpi.family_name, ^family_name_search))
    )
  end

  defp maybe_generate_ids(%{data: %{id: nil}} = changeset) do
    id = Ecto.UUID.generate()
    short_uuid = ShortUUID.encode!(id)

    changeset
    |> put_change(:id, id)
    |> put_change(:short_uuid, short_uuid)
  end

  defp maybe_generate_ids(changeset), do: changeset
end
