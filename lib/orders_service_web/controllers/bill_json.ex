defmodule OrdersServiceWeb.BillJSON do
  @moduledoc false

  alias OrdersService.Bill

  def data(%Bill{} = bill) do
    %{
      id: bill.id,
      total: bill.total,
      discount: bill.discount,
      donation: bill.donation,
      fee: bill.fee
    }
  end

  def data(_bill), do: nil

  def internal_data(bill) do
    %{
      id: bill.id,
      total: bill.total,
      discount: bill.discount,
      donation: bill.donation,
      fee: bill.fee
    }
  end
end
