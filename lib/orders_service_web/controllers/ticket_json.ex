defmodule OrdersServiceWeb.TicketJSON do
  @moduledoc false

  alias ExServiceClient.Services.EventsService.ChannelConfig
  alias ExServiceClient.Services.EventsService.SalesChannel
  alias ExServiceClient.Services.EventsService.Variant
  alias OrdersService.Events.EventHelper
  alias OrdersService.Ticket, as: TicketDB
  alias OrdersService.Tickets.Ticket
  alias OrdersServiceWeb.BillJSON
  alias OrdersServiceWeb.PersonalInformationJSON
  alias OrdersServiceWeb.RefundTransactionJSON

  def index(%{tickets: %{entries: tickets} = page}) do
    %{
      data: for(ticket <- tickets, do: data(ticket)),
      page_number: page.page_number,
      page_size: page.page_size,
      total_entries: page.total_entries,
      total_pages: page.total_pages
    }
  end

  def index(%{tickets: tickets}) do
    for(ticket <- tickets, do: data(ticket))
  end

  def internal_index(%{order_tickets: order_tickets}) do
    for(order_ticket <- order_tickets, do: internal_data(order_ticket))
  end

  def internal_pubsub_update_data_index(%{order_tickets: order_tickets}) do
    for(order_ticket <- order_tickets, do: internal_pubsub_update_data(order_ticket))
  end

  def show_details_index(%{order_tickets: order_tickets}) do
    for(order_ticket <- order_tickets, do: show_details_data(order_ticket))
  end

  def order_summary_positions(%{order_tickets: order_tickets, events: events})
      when is_list(order_tickets) and is_list(events) do
    for(event <- events, do: order_summary_position(order_tickets, event))
  end

  def order_summary_positions(_), do: nil

  def order_summary_position(order_tickets, event) do
    event_tickets = Enum.filter(order_tickets, fn order_ticket -> order_ticket.ticket.event_id == event["id"] end)

    %{
      amount: Enum.count(event_tickets),
      eventTitle: event["title"],
      startDate: event["startDate"],
      venueName: event["venue"]["name"],
      tickets: for(event_ticket <- event_tickets, do: order_summary_ticket(event_ticket.ticket))
    }
  end

  def order_summary_ticket(ticket) do
    %{
      categoryName: get_variant_name(ticket.event["variants"], ticket.variant_id),
      priceLevel: get_variant_mlpm_label(ticket.event["variants"], ticket.variant_id),
      seat: ticket.seat,
      ticketId: ticket.id
    }
  end

  def show(%{ticket: ticket}) do
    %{data: data(ticket)}
  end

  def update(%{ticket: ticket}) do
    %{
      id: ticket.id,
      order_id: ticket.order_ticket.order_id,
      event_id: ticket.event_id,
      variant_id: ticket.variant_id,
      is_checkin_allowed: ticket.status == :ACTIVE,
      check_in_date: TicketDB.find_date_for_status(ticket, :USED),
      refunded_at: TicketDB.find_date_for_status(ticket, :REFUNDED)
    }
  end

  # credo:disable-for-lines:32 Credo.Check.Refactor.CyclomaticComplexity
  def data(%{
        ticket: ticket,
        personal_information: personal_information,
        ticket_personal_information: ticket_personal_information,
        order: order
      }) do
    event_id = ticket.event_id || ticket.event_document_id

    ticket_personal_information =
      ticket_personal_information || personal_information || %OrdersService.PersonalInformation{}

    order_personal_information =
      personal_information || ticket_personal_information || %OrdersService.PersonalInformation{}

    email = (order && order.email) || order_personal_information.email || ticket_personal_information.email

    # styler:sort
    %{
      admission: ticket.admission,
      categoryId: ticket.category_id,
      checkInDate: TicketDB.find_date_for_status(ticket, :USED),
      eventId: event_id,
      id: ticket.id,
      owner: %{
        birthdate: order_personal_information.birthdate,
        email: email,
        name: String.trim("#{order_personal_information.given_name} #{order_personal_information.family_name}")
      },
      # Fallback for swap tickets, as at this moment no ticket_history entry with status_after: ACTIVE is being created
      purchaseDate: TicketDB.find_date_for_status(ticket, :ACTIVE) || ticket.inserted_at,
      refundedAt: TicketDB.find_date_for_status(ticket, :REFUNDED),
      scanCodes: get_scan_codes(ticket),
      validFor: String.trim("#{ticket_personal_information.given_name} #{ticket_personal_information.family_name}")
    }
  end

  def data(ticket) do
    # styler:sort
    %{
      admission: ticket.admission,
      attendee: ticket.attendee && PersonalInformationJSON.data(ticket.attendee),
      bill: ticket.order_ticket.bill && BillJSON.data(ticket.order_ticket.bill),
      categoryId: ticket.category_id,
      categoryName: get_category_name_from_variant(ticket.variant),
      checkInDate: TicketDB.find_date_for_status(ticket, :USED),
      id: ticket.id,
      multiLevelPricingModifierLabel: get_multi_level_pricing_modifier_label_from_variant(ticket.variant),
      refundTransactions: RefundTransactionJSON.index(%{refund_transactions: ticket.refund_transactions}),
      scanCodes: get_scan_codes(ticket),
      seat: ticket.seat,
      status: ticket.status
    }
  end

  def show_details_data(%{ticket: ticket} = order_ticket) do
    # styler:sort
    %{
      admission: ticket.admission,
      bill: BillJSON.data(order_ticket.bill),
      categoryId: ticket.category_id,
      categoryName: get_category_name_from_variant(ticket.variant),
      checkInDate: TicketDB.find_date_for_status(ticket, :USED),
      distributionType: ticket.distribution_type,
      distributionTypeId: ticket.distribution_type_id,
      distributionTypeInfo: serialize_distribution_type_info(ticket),
      eventId: ticket.event_id,
      id: ticket.id,
      multiLevelPricingModifierLabel: get_multi_level_pricing_modifier_label_from_variant(ticket.variant),
      purchaseDate: ticket.purchase_date,
      refundTransactions: RefundTransactionJSON.index(%{refund_transactions: ticket.refund_transactions}),
      scanCodes: get_scan_codes(ticket),
      seat: ticket.seat,
      status: ticket.status,
      type: ticket.type,
      variantId: ticket.variant_id
    }
  end

  def internal_data(%{ticket: ticket} = order_ticket) do
    %{
      id: ticket.id,
      event_id: ticket.event_id,
      variant_id: ticket.variant_id,
      category_id: ticket.category_id,
      donation_amount: order_ticket.donation_amount,
      purchase_date: ticket.purchase_date,
      seat: ticket.seat,
      status: ticket.status,
      type: ticket.type,
      bill: order_ticket.bill && BillJSON.internal_data(order_ticket.bill)
    }
  end

  def internal_pubsub_update_data(%{ticket: %{attendee: attendee, owner: owner} = ticket} = order_ticket) do
    personal_information = attendee || owner || %{given_name: "", family_name: ""}
    variant = EventHelper.get_variant_from_event(ticket.event, ticket.variant_id)

    # styler:sort
    %{
      admission: ticket.admission,
      allEntranceAreasAllowed: variant["allEntranceAreasAllowed"],
      bill: order_ticket.bill && BillJSON.internal_data(order_ticket.bill),
      category_hint: variant["hint"],
      category_id: ticket.category_id,
      category_name: variant["name"],
      donation_amount: order_ticket.donation_amount,
      entranceAreas: variant["entranceAreas"],
      event: internal_ticket_event(ticket),
      event_id: ticket.event_id,
      id: ticket.id,
      mlp_label: variant["multiLevelPricingModifier"]["label"],
      purchase_date: ticket.purchase_date,
      seat: ticket.seat,
      status: ticket.status,
      token: Ticket.get_ticket_token(ticket),
      type: ticket.type,
      valid_for: String.trim("#{personal_information.given_name} #{personal_information.family_name}"),
      variant_id: ticket.variant_id
    }
  end

  def show_compatible(%{
        ticket: %{
          ticket: ticket,
          order: order,
          ticket_owner: ticket_owner,
          category_name: category_name,
          mlpm_label: mlpm_label
        }
      }) do
    %{
      attendee: %{
        name: ticket_owner.given_name,
        email: (order && order.email) || (ticket_owner && ticket_owner.email),
        checkedIn:
          if is_nil(TicketDB.find_date_for_status(ticket, :USED)) do
            false
          else
            true
          end,
        birthdate: ticket_owner.birthdate,
        checkInDate: TicketDB.find_date_for_status(ticket, :USED),
        # Fallback for swap tickets, as at this moment no ticket_history entry
        # with status_after: ACTIVE is being created
        purchaseDate: TicketDB.find_date_for_status(ticket, :ACTIVE) || ticket.inserted_at,
        id: ticket.id,
        refunded:
          if is_nil(TicketDB.find_date_for_status(ticket, :REFUNDED)) do
            false
          else
            true
          end,
        refundDate: TicketDB.find_date_for_status(ticket, :REFUNDED),
        categoryId: ticket.category_id,
        categoryName: category_name,
        multiLevelPricingModifierLabel: mlpm_label,
        admission: ticket.admission,
        seat: ticket.seat
      }
    }
  end

  def ticket_export(%{tickets: tickets, page: page, type: type}) when is_map(page) do
    %{
      data: for(ticket <- tickets, do: ticket_export_data(ticket, type)),
      pageNumber: page.page_number,
      pageSize: page.page_size,
      totalEntries: page.total_entries,
      totalPages: page.total_pages
    }
  end

  def ticket_export(%{tickets: tickets, page: _page, type: type}) do
    %{
      data: for(ticket <- tickets, do: ticket_export_data(ticket, type))
    }
  end

  def ticket_export_data(ticket, "scan_export") do
    %{
      id: ticket.id,
      categoryName: ticket.categoryName,
      isValidForAdmission: ticket.isValidForAdmission,
      purchaseDate: ticket.purchaseDate,
      scanStatus: ticket.scanStatus,
      seat: ticket.seat,
      status: ticket.status
    }
  end

  def ticket_export_data(ticket, "full_export") do
    %{
      ticket_id: ticket.ticket_id,
      order_id: ticket.order_id,
      event_title: ticket.event_title,
      ticket_category: ticket.product,
      sales_channel: ticket.sales_channel,
      guestlist: ticket.guestlist,
      delivery_type: ticket.delivery_type,
      ticket_price: ticket.ticket_price,
      kickback: ticket.kickback,
      presale_fee: ticket.presale_fee,
      system_fee: ticket.system_fee,
      hard_ticket_fee: ticket.hard_ticket_fee,
      voucher_value: ticket.voucher_value,
      voucher_description: ticket.voucher_description,
      total_price: ticket.total_price,
      purchase_date: ticket.purchase_date,
      check_in_date: ticket.check_in_date,
      payment_method: ticket.payment_method,
      order_email: ticket.order_email,
      company: ticket.company,
      street: ticket.street,
      postal_code: ticket.postal_code,
      city: ticket.city,
      birthdate: ticket.birthdate
    }
  end

  def complete_tickets(%{tickets: tickets}) do
    %{
      tickets: for(ticket <- tickets, do: complete_ticket_data(ticket))
    }
  end

  defp complete_ticket_data(
         %{ticket: ticket, owner: owner, attendee: attendee, order: order, bill: bill, order_ticket: order_ticket} =
           _ticket
       ) do
    %{
      ticket: %{
        id: ticket.id,
        event_id: ticket.event_id,
        order_id: order_ticket.order_id,
        category_id: ticket.category_id,
        variant_id: ticket.variant_id,
        voucher_id: ticket.voucher_id,
        bill_id: order_ticket.bill_id,
        is_checkin_allowed: ticket.status == :ACTIVE,
        check_in_date: TicketDB.find_date_for_status(ticket, :USED),
        donation_amount: order_ticket.donation_amount,
        donation_recipient_id: order_ticket.donation_recipient_id,
        purchase_date: TicketDB.find_date_for_status(ticket, :ACTIVE)
      },
      owner: %{
        id: owner.id,
        user_id: owner.user_id,
        user_document_id: owner.user_document_id,
        gender: owner.gender,
        given_name: owner.given_name,
        family_name: owner.family_name,
        phone_number: owner.phone_number,
        birthdate: owner.birthdate
      },
      attendee: %{
        id: attendee.id,
        user_id: attendee.user_id,
        gender: attendee.gender,
        given_name: attendee.given_name,
        family_name: attendee.family_name,
        phone_number: attendee.phone_number,
        birthdate: attendee.birthdate
      },
      order: %{
        id: order.id,
        bill_id: order.bill_id,
        created_by: order.created_by,
        email: order.email,
        paid_date: order.paid_date,
        payer_email: order.payer_email,
        status: order.status,
        submitted_date: order.submitted_date
      },
      bill: %{
        id: bill.id,
        total: bill.total,
        discount: bill.discount,
        fee: bill.fee,
        presale_fee: bill.presale_fee,
        presale_fee_tax: bill.presale_fee_tax,
        promoter_total: bill.promoter_total,
        promoter_net_total: bill.promoter_net_total,
        promoter_tax: bill.promoter_tax,
        system_fee: bill.system_fee,
        system_fee_tax: bill.system_fee_tax
      }
    }
  end

  def invitation_tickets(%{tickets: tickets}) do
    %{
      tickets: for(ticket <- tickets, do: invitation_ticket_data(ticket))
    }
  end

  defp invitation_ticket_data(%{ticket: ticket} = _ticket) do
    %{
      ticket: %{
        id: ticket.id,
        is_checkin_allowed: ticket.status == :ACTIVE,
        check_in_date: TicketDB.find_date_for_status(ticket, :USED),
        status: ticket.status,
        attendee: %{
          id: ticket.attendee.id,
          user_id: ticket.attendee.user_id,
          user_document_id: ticket.attendee.user_document_id,
          given_name: ticket.attendee.given_name,
          family_name: ticket.attendee.family_name
        }
      }
    }
  end

  defp internal_ticket_event(%{event: event} = _ticket) do
    %{
      id: event["id"],
      title: event["title"],
      promoter_name: event["promoter"]["name"],
      admission_date: event["admissionDate"],
      start_date: event["startDate"],
      end_date: event["endDate"],
      venue: internal_ticket_event_venue(event)
    }
  end

  defp internal_ticket_event(_ticket), do: nil

  defp internal_ticket_event_venue(%{"venue" => venue} = _event) do
    %{
      name: venue["name"],
      street: "#{venue["street"]} #{venue["houseNumber"]}",
      zip_code: venue["zipCode"],
      city: venue["city"]
    }
  end

  defp internal_ticket_event_venue(_event), do: nil

  defp get_variant_name(nil, _variant_id), do: nil

  defp get_variant_name(variants, variant_id) do
    Enum.find_value(variants, fn variant ->
      if variant["variantId"] == variant_id, do: variant["name"]
    end)
  end

  defp get_variant_mlpm_label(nil, _variant_id), do: nil

  defp get_variant_mlpm_label(variants, variant_id) do
    Enum.find_value(variants, fn variant ->
      if variant["variantId"] == variant_id, do: variant["multiLevelPricingModifier"]["label"]
    end)
  end

  defp get_category_name_from_variant(%Variant{ticketCategory: %{name: name}}), do: name
  defp get_category_name_from_variant(_variant), do: nil

  defp get_multi_level_pricing_modifier_label_from_variant(%Variant{multiLevelPricingModifier: %{label: label}}),
    do: label

  defp get_multi_level_pricing_modifier_label_from_variant(_variant), do: nil

  defp get_scan_codes(%TicketDB{scan_codes: scan_codes}) when is_list(scan_codes),
    do: Enum.map(scan_codes, & &1.scan_code)

  defp get_scan_codes(_ticket), do: []

  defp serialize_distribution_type_info(%{distribution_type_info: %SalesChannel{} = sales_channel}) do
    %{
      id: sales_channel.id,
      originalPrice: sales_channel.originalPrice,
      quotaMode: sales_channel.quotaMode,
      variantId: sales_channel.variantId,
      channelConfigId: sales_channel.channelConfigId,
      channelConfig: serialize_channel_config(sales_channel.channelConfig)
    }
  end

  defp serialize_distribution_type_info(_ticket), do: nil

  defp serialize_channel_config(%ChannelConfig{} = channel_config) do
    %{
      id: channel_config.id,
      type: channel_config.type,
      value: channel_config.value,
      label: channel_config.label,
      description: channel_config.description,
      color: channel_config.color,
      amountOfObjects: channel_config.amountOfObjects,
      eventId: channel_config.eventId
    }
  end

  defp serialize_channel_config(_channel_config), do: nil
end
