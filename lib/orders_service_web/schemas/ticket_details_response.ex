defmodule OrdersServiceWeb.Schemas.TicketDetailsResponse do
  @moduledoc false
  alias OpenApiSpex.Schema

  require OpenApiSpex

  defmodule DistributionTypeInfo do
    @moduledoc false
    alias OpenApiSpex.Schema

    require OpenApiSpex

    defmodule ChannelConfig do
      @moduledoc false
      alias OpenApiSpex.Schema

      require OpenApiSpex

      OpenApiSpex.schema(%{
        title: "ChannelConfig",
        description: "Channel configuration details",
        type: :object,
        properties: %{
          id: %Schema{type: :string, description: "Channel config ID"},
          type: %Schema{type: :string, description: "Channel type"},
          value: %Schema{type: :number, description: "Channel value"},
          label: %Schema{type: :string, description: "Channel label"},
          description: %Schema{type: :string, description: "Channel description"},
          color: %Schema{type: :string, description: "Channel color"},
          amountOfObjects: %Schema{type: :integer, description: "Amount of objects"},
          eventId: %Schema{type: :string, description: "Event ID"}
        }
      })
    end

    OpenApiSpex.schema(%{
      title: "DistributionTypeInfo",
      description: "Distribution type information",
      type: :object,
      oneOf: [
        # Currently not implemented for REGULAR and GUEST_LIST_INVITATION
        %Schema{
          title: "SalesChannel",
          type: :object,
          properties: %{
            id: %Schema{type: :string, description: "Sales channel ID"},
            originalPrice: %Schema{type: :integer, description: "Original price in cents"},
            quotaMode: %Schema{type: :string, enum: ["RESERVED", "SHARED"], description: "Quota mode"},
            variantId: %Schema{type: :string, description: "Variant ID"},
            channelConfigId: %Schema{type: :string, description: "Channel config ID"},
            channelConfig: ChannelConfig
          }
        }
      ]
    })
  end

  OpenApiSpex.schema(%{
    title: "TicketDetailsResponse",
    description: "Ticket details with distribution type information",
    type: :object,
    properties: %{
      id: %Schema{type: :string, description: "Ticket ID"},
      admission: %Schema{type: :boolean, description: "Whether ticket grants admission"},
      bill: OrdersServiceWeb.Schemas.BillResponse,
      categoryId: %Schema{type: :string, description: "Category ID"},
      categoryName: %Schema{type: :string, description: "Category name", nullable: true},
      checkInDate: %Schema{type: :string, format: :"date-time", description: "Check-in date", nullable: true},
      distributionType: %Schema{
        type: :string,
        enum: ["REGULAR", "SALES_CHANNEL", "GUEST_LIST_INVITATION"],
        description: "Distribution type"
      },
      distributionTypeId: %Schema{type: :string, description: "Distribution type ID", nullable: true},
      distributionTypeInfo: %Schema{oneOf: [DistributionTypeInfo], nullable: true},
      eventId: %Schema{type: :string, description: "Event ID"},
      multiLevelPricingModifierLabel: %Schema{
        type: :string,
        description: "Multi-level pricing modifier label",
        nullable: true
      },
      purchaseDate: %Schema{type: :string, format: :"date-time", description: "Purchase date"},
      refundTransactions: %Schema{
        type: :array,
        description: "List of refund transactions",
        items: OrdersServiceWeb.Schemas.RefundTransactionResponse
      },
      scanCodes: %Schema{
        type: :array,
        description: "List of scan codes",
        items: %Schema{type: :string}
      },
      seat: %Schema{type: :string, description: "Seat information", nullable: true},
      status: %Schema{
        type: :string,
        enum: ["ACTIVE", "DEFRAUDED", "REFUNDED", "REFUNDING", "USED", "UNUSED"],
        description: "Ticket status"
      },
      type: %Schema{
        type: :string,
        enum: ["DIGITAL", "HARD"],
        description: "Ticket type"
      },
      variantId: %Schema{type: :string, description: "Variant ID"}
    },
    required: [:id, :distributionType, :eventId, :status, :type, :variantId],
    example: %{
      id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5c",
      admission: true,
      categoryId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5a",
      categoryName: "General Admission",
      distributionType: "SALES_CHANNEL",
      distributionTypeId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
      distributionTypeInfo: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
        originalPrice: 2500,
        quotaMode: "RESERVED",
        variantId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5d",
        channelConfigId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5e",
        channelConfig: %{
          id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5e",
          type: "standard",
          value: 10.0,
          label: "Premium Channel",
          description: "Premium sales channel",
          color: "#FF0000",
          amountOfObjects: 100,
          eventId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5f"
        }
      },
      eventId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5f",
      purchaseDate: "2023-01-01T12:00:00Z",
      status: "ACTIVE",
      type: "DIGITAL",
      variantId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5d"
    }
  })
end
