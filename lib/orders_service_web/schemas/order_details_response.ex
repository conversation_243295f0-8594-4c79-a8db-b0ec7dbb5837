defmodule OrdersServiceWeb.Schemas.OrderDetailsResponse do
  @moduledoc false
  alias OpenApiSpex.Schema

  require OpenApiSpex

  defmodule BillResponse do
    @moduledoc false
    alias OpenApiSpex.Schema

    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "BillResponse",
      description: "Bill details",
      type: :object,
      # styler:sort
      properties: %{
        fee: %Schema{type: :integer, description: "Fee amount"},
        id: %Schema{description: "Bill ID", type: :string},
        presaleFee: %Schema{type: :integer, description: "Presale fee amount"},
        presaleFeeTax: %Schema{type: :integer, description: "Presale fee tax amount"},
        systemFee: %Schema{type: :integer, description: "System fee amount"},
        systemFeeTax: %Schema{type: :integer, description: "System fee tax amount"},
        total: %Schema{type: :integer, description: "Total amount"}
      }
    })
  end

  OpenApiSpex.schema(%{
    title: "OrderDetailsResponse",
    description: "Order details",
    type: :object,
    # styler:sort
    properties: %{
      bill: BillResponse,
      billId: %Schema{type: :string, description: "Bill ID"},
      billingAddress: OrdersServiceWeb.Schemas.BillingAddressResponse,
      createdByPersonalInformation: OrdersServiceWeb.Schemas.PersonalInformationResponse,
      deliveryAddress: OrdersServiceWeb.Schemas.OrderListResponse.AddressResponse,
      email: %Schema{type: :string, description: "Email of the order"},
      id: %Schema{type: :string, description: "Order ID"},
      invitationOrders: %Schema{
        type: :array,
        description: "List of invitation orders",
        items: OrdersServiceWeb.Schemas.InvitationOrderResponse
      },
      payinTransactions: %Schema{
        type: :array,
        description: "List of payin transactions",
        items: OrdersServiceWeb.Schemas.PayinTransactionResponse
      },
      status: %Schema{type: :string, description: "Order status"},
      submittedDate: %Schema{type: :string, format: :"date-time", description: "UTC Time when the order was submitted"},
      tickets: %Schema{
        type: :array,
        description: "List of tickets with distribution type information",
        items: OrdersServiceWeb.Schemas.TicketDetailsResponse
      }
    },
    # styler:sort
    example: %{
      bill: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
        fee: 500,
        presaleFee: 200,
        presaleFeeTax: 40,
        systemFee: 300,
        systemFeeTax: 60,
        total: 1100
      },
      billId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
      billingAddress: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5e",
        streetAddress: "123 Main St",
        locality: "Anytown",
        region: "CA",
        postalCode: "12345",
        countryIso: "US"
      },
      createdByPersonalInformation: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e59",
        givenName: "John",
        familyName: "Doe"
      },
      deliveryAddress: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5f",
        streetAddress: "456 Oak St",
        locality: "Othertown",
        region: "NY",
        postalCode: "67890",
        countryIso: "US"
      },
      email: "<EMAIL>",
      id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5c",
      invitationOrders: [
        %{
          guestlistId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5a",
          id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
          invitationId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5c",
          orderId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5d"
        }
      ],
      payinTransactions: [
        %{
          id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5g",
          status: "completed",
          paymentMethod: "credit_card"
        }
      ],
      status: "PAID",
      submittedDate: "2022-01-01T00:00:00Z",
      tickets: [
        %{
          id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5c",
          admission: true,
          categoryId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5a",
          categoryName: "General Admission",
          distributionType: "SALES_CHANNEL",
          distributionTypeId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
          distributionTypeInfo: %{
            id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
            originalPrice: 2500,
            quotaMode: "RESERVED",
            variantId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5d",
            channelConfigId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5e",
            channelConfig: %{
              id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5e",
              type: "standard",
              value: 10.0,
              label: "Premium Channel",
              description: "Premium sales channel",
              color: "#FF0000",
              amountOfObjects: 100,
              eventId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5f"
            }
          },
          eventId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5f",
          purchaseDate: "2023-01-01T12:00:00Z",
          status: "ACTIVE",
          type: "DIGITAL",
          variantId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5d"
        },
        %{
          id: "ebc5feb5-07cc-4d52-9192-457a3dc64e6c",
          admission: true,
          categoryId: "ebc5feb5-07cc-4d52-9192-457a3dc64e6a",
          categoryName: "VIP",
          distributionType: "REGULAR",
          distributionTypeId: nil,
          distributionTypeInfo: nil,
          eventId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5f",
          purchaseDate: "2023-01-01T12:00:00Z",
          status: "ACTIVE",
          type: "DIGITAL",
          variantId: "ebc5feb5-07cc-4d52-9192-457a3dc64e6d"
        }
      ]
    }
  })
end
