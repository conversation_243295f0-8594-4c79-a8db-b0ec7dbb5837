defmodule EventsServiceWeb.ApiSchemas.Services.SalesChannelSchema do
  @moduledoc false
  alias EventsServiceWeb.ApiSchemas.Services.ChannelConfigSchema.ChannelConfig
  alias OpenApiSpex.Schema

  defmodule SalesChannel do
    @moduledoc false
    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "Sales Channel",
      description: "Sales channel entity",
      type: :object,
      properties: %{
        id: %Schema{type: :string, format: :uuid, description: "Sales Channel ID"},
        originalPrice: %Schema{type: :integer, description: "Original price in cents"},
        quotaMode: %Schema{type: :string, enum: ["RESERVED", "SHARED"], description: "Quota mode"},
        variantId: %Schema{type: :string, format: :uuid, description: "Variant ID"},
        channelConfig: ChannelConfig,
        channelConfigId: %Schema{type: :string, format: :uuid, description: "Channel Config ID"},
        insertedAt: %Schema{type: :string, format: :"date-time", description: "Inserted at"},
        updatedAt: %Schema{type: :string, format: :"date-time", description: "Updated at"}
      },
      example: %{
        "id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "originalPrice" => 2500,
        "quotaMode" => "RESERVED",
        "variantId" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "channelConfig" => %{
          "id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
          "token" => "channel_token_123",
          "channelKey" => "channel_key_123",
          "type" => "standard",
          "value" => "some_value",
          "label" => "Channel Label",
          "description" => "Channel Configuration Description",
          "color" => "#FF0000",
          "amountOfObjects" => 100,
          "validUntil" => "2024-12-31T23:59:59Z",
          "eventId" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
        },
        "channelConfigId" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "insertedAt" => "2023-01-01T00:00:00Z",
        "updatedAt" => "2023-01-01T00:00:00Z"
      }
    })
  end
end
