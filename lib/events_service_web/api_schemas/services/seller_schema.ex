defmodule EventsServiceWeb.ApiSchemas.Services.SellerSchema do
  @moduledoc """
  API schemas for Seller entities
  """
  alias OpenApiSpex.Schema

  require OpenApiSpex

  defmodule Address do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "Address",
      description: "Address information",
      type: :object,
      properties: %{
        streetAddress: %Schema{type: :string, description: "Street address"},
        locality: %Schema{type: :string, description: "City or locality"},
        postalCode: %Schema{type: :string, description: "Postal code"},
        countryIso: %Schema{type: :string, description: "Country ISO code"}
      },
      required: [:streetAddress, :locality, :postalCode, :countryIso]
    })
  end

  defmodule StoreData do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "StoreData",
      description: "Store-specific seller data",
      type: :object,
      properties: %{
        address: Address,
        name: %Schema{type: :string, description: "Store name"}
      },
      required: [:address]
    })
  end

  defmodule Seller do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "Seller",
      description: "A seller entity",
      type: :object,
      properties: %{
        id: %Schema{type: :string, format: :uuid, description: "Seller ID"},
        type: %Schema{
          type: :string,
          enum: ["STORE", "ORGANIZER", "PLATFORM", "SHOP", "POS", "RESELLER"],
          description: "Seller type"
        },
        typeId: %Schema{type: :string, format: :uuid, description: "Type-specific ID"},
        parentId: %Schema{type: :string, format: :uuid, description: "Parent seller ID"},
        ownerId: %Schema{type: :string, format: :uuid, description: "Owner ID"},
        insertedAt: %Schema{type: :string, format: :date_time, description: "Creation timestamp"},
        updatedAt: %Schema{type: :string, format: :date_time, description: "Last update timestamp"},
        sellerData: %Schema{
          oneOf: [
            StoreData
          ],
          discriminator: %{
            propertyName: "type",
            mapping: %{
              "STORE" => "#/components/schemas/StoreData"
            }
          }
        }
      },
      required: [:id, :type, :typeId, :ownerId, :insertedAt, :updatedAt],
      example: %{
        "id" => "123e4567-e89b-12d3-a456-************",
        "type" => "STORE",
        "typeId" => "987e6543-e21b-34d3-c456-************",
        "parentId" => "456e7890-e12b-45d3-b456-************",
        "ownerId" => "789e0123-e34b-56d3-d456-************",
        "insertedAt" => "2025-04-16T09:27:00Z",
        "updatedAt" => "2025-04-16T10:00:00Z",
        "sellerData" => %{
          "address" => %{
            "streetAddress" => "123 Main St",
            "locality" => "Springfield",
            "postalCode" => "12345",
            "countryIso" => "US"
          },
          "name" => "My Store"
        }
      }
    })
  end

  defmodule CreateSellerRequest do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "CreateSellerRequest",
      description: "Request body for creating a seller",
      type: :object,
      properties: %{
        type: %Schema{
          type: :string,
          enum: ["STORE", "ORGANIZER", "PLATFORM", "SHOP", "POS", "RESELLER"],
          description: "Seller type"
        },
        ownerId: %Schema{
          type: :string,
          format: :uuid,
          description: "Owner ID"
        },
        parentId: %Schema{
          type: :string,
          format: :uuid,
          description: "Parent seller ID",
          nullable: true
        },
        sellerData: %Schema{
          oneOf: [
            StoreData
          ],
          discriminator: %{
            propertyName: "type",
            mapping: %{
              "STORE" => "#/components/schemas/StoreData"
            }
          }
        }
      },
      required: [:type, :ownerId, :sellerData],
      example: %{
        "type" => "STORE",
        "ownerId" => "789e0123-e34b-56d3-d456-************",
        "parentId" => "456e7890-e12b-45d3-b456-************",
        "sellerData" => %{
          "address" => %{
            "streetAddress" => "123 Main St",
            "locality" => "Springfield",
            "postalCode" => "12345",
            "countryIso" => "US"
          },
          "name" => "My Store"
        }
      }
    })
  end

  defmodule SellerPermissionCheck do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "SellerPermissionCheck",
      description: "Request body for checking seller permissions",
      type: :object,
      properties: %{
        sellerId: %Schema{
          type: :string,
          format: :uuid,
          description: "ID of the seller to check permissions for"
        },
        eventIds: %Schema{
          type: :array,
          items: %Schema{type: :string, format: :uuid},
          minItems: 1,
          description: "List of event IDs to check permissions against"
        },
        scope: %Schema{
          type: :string,
          enum: ["events"],
          description: "The scope of permissions to check. Currently only 'events' is supported"
        }
      },
      required: [:seller_id, :event_ids, :scope],
      example: %{
        "sellerId" => "123e4567-e89b-12d3-a456-************",
        "eventIds" => [
          "987e6543-e21b-34d3-c456-************",
          "456e7890-e12b-45d3-b456-************"
        ],
        "scope" => "events"
      }
    })
  end

  defmodule SellerPermissionCheckResponse do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "SellerPermissionCheckResponse",
      description: "Response for a seller permission check request",
      type: :object,
      properties: %{
        hasPermission: %Schema{
          type: :boolean,
          description: "Whether the seller has the requested permission for any of the specified events"
        }
      },
      required: [:has_permission],
      example: %{
        "hasPermission" => true
      }
    })
  end
end
