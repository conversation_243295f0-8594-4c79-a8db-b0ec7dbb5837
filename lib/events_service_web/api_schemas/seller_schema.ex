defmodule EventsServiceWeb.ApiSchemas.SellerSchema do
  @moduledoc false

  alias EventsServiceWeb.ApiSchemas.PromoterSchema.PromoterDetails
  alias EventsServiceWeb.ApiSchemas.Services.AddressSchema.Address
  alias OpenApiSpex.Schema

  require OpenApiSpex

  defmodule SellerData do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "SellerData",
      description: "Type-specific data for a seller",
      type: :object,
      oneOf: [
        %Schema{
          title: "StoreData",
          type: :object,
          properties: %{
            name: %Schema{type: :string, description: "Store name"},
            address: Address
          }
        },
        %Schema{
          title: "OrganizerData",
          type: :object,
          properties: %{
            id: %Schema{type: :string, format: :uuid, description: "Organizer ID"},
            promoter: PromoterDetails
          }
        }
      ],
      discriminator: %{
        propertyName: "type"
      }
    })
  end

  defmodule Seller do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "Seller",
      description: "A seller entity",
      type: :object,
      properties: %{
        id: %Schema{type: :string, format: :uuid, description: "Seller ID"},
        type: %Schema{type: :string, enum: ["STORE", "ORGANIZER"], description: "Type of seller"},
        typeId: %Schema{type: :string, format: :uuid, description: "ID of the type-specific entity"},
        parentId: %Schema{type: :string, format: :uuid, nullable: true, description: "Parent seller ID"},
        ownerId: %Schema{type: :string, format: :uuid, description: "Owner user ID"},
        sellerData: SellerData,
        insertedAt: %Schema{type: :string, format: :"date-time", description: "Creation timestamp"},
        updatedAt: %Schema{type: :string, format: :"date-time", description: "Last update timestamp"}
      },
      required: [:id, :type, :typeId, :ownerId, :insertedAt, :updatedAt],
      example: %{
        "id" => "123e4567-e89b-12d3-a456-************",
        "type" => "ORGANIZER",
        "typeId" => "123e4567-e89b-12d3-a456-************",
        "parentId" => nil,
        "ownerId" => "123e4567-e89b-12d3-a456-************",
        "sellerData" => %{
          "id" => "423e4567-e89b-12d3-a456-************",
          "promoter" => %{
            "id" => "123e4567-e89b-12d3-a456-************",
            "name" => "Amsterdam Music Events",
            "displayName" => "Amsterdam Music Events",
            "givenName" => "John",
            "familyName" => "Doe",
            "companyName" => "Amsterdam Music Events Ltd",
            "storeUrl" => "https://amsterdammusicevents.com",
            "verified" => true,
            "entityType" => "organization",
            "address" => "Main Street",
            "city" => "Amsterdam",
            "country" => "Netherlands",
            "streetNumber" => "123",
            "zipCode" => "1012 AB",
            "insertedAt" => "2023-01-01T00:00:00Z",
            "updatedAt" => "2023-01-01T00:00:00Z"
          }
        },
        "insertedAt" => "2023-01-01T00:00:00Z",
        "updatedAt" => "2023-01-01T00:00:00Z"
      }
    })
  end

  defmodule CreateSellerRequest do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "CreateSellerRequest",
      description: "Request body for creating a seller",
      type: :object,
      properties: %{
        type: %Schema{
          type: :string,
          enum: ["ORGANIZER"],
          description: "Seller type (currently only ORGANIZER is supported)"
        },
        promoterId: %Schema{
          type: :string,
          format: :uuid,
          description: "Promoter ID"
        },
        displayName: %Schema{
          type: :string,
          description: "Display name for the organizer"
        }
      },
      required: [:type, :promoterId],
      example: %{
        "type" => "ORGANIZER",
        "promoterId" => "123e4567-e89b-12d3-a456-************",
        "displayName" => "My Event Organization"
      }
    })
  end

  defmodule SellersResponse do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "SellersResponse",
      description: "Paginated list of sellers",
      type: :object,
      properties: %{
        data: %Schema{
          type: :array,
          items: Seller,
          description: "List of sellers"
        },
        pageNumber: %Schema{type: :integer, description: "Current page number"},
        pageSize: %Schema{type: :integer, description: "Number of items per page"},
        totalEntries: %Schema{type: :integer, description: "Total number of entries"},
        totalPages: %Schema{type: :integer, description: "Total number of pages"}
      },
      required: [:data, :pageNumber, :pageSize, :totalEntries, :totalPages],
      example: %{
        "data" => [
          %{
            "id" => "123e4567-e89b-12d3-a456-************",
            "type" => "ORGANIZER",
            "typeId" => "123e4567-e89b-12d3-a456-************",
            "parentId" => nil,
            "ownerId" => "123e4567-e89b-12d3-a456-************",
            "sellerData" => %{
              "promoterId" => "123e4567-e89b-12d3-a456-************",
              "promoter" => %{
                "id" => "123e4567-e89b-12d3-a456-************",
                "name" => "Amsterdam Music Events",
                "displayName" => "Amsterdam Music Events",
                "givenName" => "John",
                "familyName" => "Doe",
                "companyName" => "Amsterdam Music Events Ltd",
                "storeUrl" => "https://amsterdammusicevents.com",
                "verified" => true,
                "entityType" => "organization",
                "address" => "Main Street",
                "city" => "Amsterdam",
                "country" => "Netherlands",
                "streetNumber" => "123",
                "zipCode" => "1012 AB",
                "insertedAt" => "2023-01-01T00:00:00Z",
                "updatedAt" => "2023-01-01T00:00:00Z"
              }
            },
            "insertedAt" => "2023-01-01T00:00:00Z",
            "updatedAt" => "2023-01-01T00:00:00Z"
          },
          %{
            "id" => "223e4567-e89b-12d3-a456-************",
            "type" => "STORE",
            "typeId" => "223e4567-e89b-12d3-a456-************",
            "parentId" => nil,
            "ownerId" => "223e4567-e89b-12d3-a456-************",
            "sellerData" => %{
              "name" => "Amsterdam Record Store",
              "address" => %{
                "street" => "Shopping Street",
                "number" => "45",
                "city" => "Amsterdam",
                "country" => "Netherlands",
                "zipCode" => "1013 CD"
              }
            },
            "insertedAt" => "2023-01-01T00:00:00Z",
            "updatedAt" => "2023-01-01T00:00:00Z"
          }
        ],
        "pageNumber" => 1,
        "pageSize" => 10,
        "totalEntries" => 2,
        "totalPages" => 1
      }
    })
  end

  defmodule Event do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "SellerEvent",
      description: "Event summary information",
      type: :object,
      properties: %{
        id: %Schema{type: :string, format: :uuid, description: "Event ID"},
        title: %Schema{type: :string, description: "Event title"},
        startDate: %Schema{type: :string, format: "date-time", description: "Event start date and time"},
        venueCity: %Schema{type: :string, nullable: true, description: "City where the venue is located"},
        availableTickets: %Schema{
          type: :integer,
          description: "Number of available tickets (quota - sold)"
        },
        venueName: %Schema{type: :string, nullable: true, description: "Name of the venue"}
      },
      example: %{
        "id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "title" => "Summer Concert Series",
        "startDate" => "2023-07-15T19:00:00Z",
        "venueCity" => "Amsterdam",
        "venueName" => "Concert Hall",
        "availableTickets" => 150
      }
    })
  end

  defmodule SellerEventsResponse do
    @moduledoc false
    OpenApiSpex.schema(%{
      title: "SellerEventsResponse",
      description: "Response containing a list of events for a seller",
      type: :object,
      properties: %{
        data: %Schema{type: :array, items: Event, description: "List of events"},
        page: %Schema{type: :integer, description: "Current page number"},
        pageSize: %Schema{type: :integer, description: "Number of items per page"},
        totalPages: %Schema{type: :integer, description: "Total number of pages"},
        totalEntries: %Schema{type: :integer, description: "Total number of events matching the criteria"}
      },
      example: %{
        "data" => [
          %{
            "id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
            "title" => "Summer Concert Series",
            "startDate" => "2023-07-15T19:00:00Z",
            "venueCity" => "Amsterdam",
            "venueName" => "Concert Hall",
            "availableTickets" => 150
          }
        ],
        "page" => 1,
        "pageSize" => 10,
        "totalPages" => 3,
        "totalEntries" => 25
      }
    })
  end
end
