defmodule EventsServiceWeb.Router do
  use EventsServiceWeb, :router

  import Plug.BasicAuth

  alias EventsService.RBACClient
  alias EventsServiceWeb.Plugs.CacheHeaders
  alias EventsServiceWeb.Plugs.SellerContext
  alias ExIkarus.Plug.EnableExperimental
  alias ExIkarus.Plug.SetDeprecated
  alias ExRBAC.Plug.Authenticate
  alias ExRBAC.Plug.AuthenticationDispatcher
  alias ExRBAC.Plug.EnsureAuthenticated

  pipeline :api do
    plug :accepts, ["json"]
  end

  pipeline :pdf_pipeline do
    plug :accepts, ["pdf"]
  end

  pipeline :webhook do
    plug EventsServiceWeb.Plugs.BalancePlatformAuthPlug
  end

  scope "/api", EventsServiceWeb do
    pipe_through :api
  end

  pipeline :api_doc do
    plug OpenApiSpex.Plug.PutApiSpec, module: EventsServiceWeb.ApiSpec
  end

  pipeline :migration do
    plug :basic_auth, Application.compile_env(:events_service, :basic_auth)
  end

  pipeline :user_authentication do
    plug :fetch_session
    plug :authentication_dispatcher
    plug :authenticate
    plug EnsureAuthenticated
    plug SellerContext
  end

  pipeline :verify_header do
    plug :fetch_session
    plug :authentication_dispatcher
    plug :authenticate
  end

  pipeline :enable_experimental do
    plug EnableExperimental
  end

  pipeline :deprecated do
    plug SetDeprecated
  end

  pipeline :snake_case do
    plug ProperCase.Plug.SnakeCaseParams
  end

  pipeline :cache_headers do
    plug CacheHeaders
  end

  scope "/events" do
    scope "/api" do
      get "/swaggerui", OpenApiSpex.Plug.SwaggerUI,
        path: "/events/api/openapi",
        tagsSorter: "alpha"
    end

    scope "/api" do
      pipe_through [:api, :api_doc]
      get "/openapi", OpenApiSpex.Plug.RenderSpec, []
    end
  end

  scope "/events", EventsServiceWeb do
    pipe_through :api

    scope "/api/mgmt" do
      pipe_through :migration
      get "/health", HealthController, :show
      get "/migrations/:id", PostgresController, :migration
      get "/db/sql", PostgresController, :run_seed
    end

    scope "/api" do
      post "/scripts", ScriptController, :run
    end

    scope "/api" do
      pipe_through :webhook
      post "/webhooks", BalancePlatformCallbackController, :webhook_callback
    end
  end

  scope "/events/api/payment", EventsServiceWeb do
    pipe_through [:pdf_pipeline, :user_authentication]
    get "/transfers/:id/invoice", PayoutTransactionInvoiceController, :show
  end

  scope "/events", EventsServiceWeb do
    pipe_through :api

    scope "/api" do
      pipe_through :enable_experimental

      get "/health", HealthController, :show
      get "/location", LocationController, :index

      scope "/payment" do
        pipe_through :user_authentication

        get "/transfers/payment_instruments",
            PayoutTransactionsController,
            :get_payment_instruments

        get "/transfers", PayoutTransactionsController, :index
        post "/transfers", PayoutTransactionsController, :create
        get "/transfers/:id", PayoutTransactionsController, :show
      end

      scope "/payment/balance-platform/webhook" do
        pipe_through [:webhook]
        post "/", BalancePlatformCallbackController, :webhook_callback
      end

      get "/donation-recipients/:id", DonationRecipientController, :show

      scope "/venues" do
        get "/:id", VenueController, :show
        get "/:id/charts", VenueController, :charts
        post "/search", VenueController, :search

        scope "/" do
          pipe_through :user_authentication
          post "/create", VenueController, :create
        end
      end

      scope "/cities" do
        pipe_through [:user_authentication, :snake_case]
        get "/", VenueController, :cities
      end

      get "/countries", CountryController, :index
      get "/countries/:iso", CountryController, :show

      scope "/promoter" do
        pipe_through :user_authentication
        post "/", PromoterController, :create
        get "/onboarding/resume", PromoterController, :resume
        get "/legal-entity", PromoterController, :get_legal_entity
        get "/validate-legal-entity", PromoterController, :validate_legal_entity
        get "/events", PromoterController, :events
        get "/events/count", PromoterController, :events_count
        get "/userAssigned", PromoterController, :user_assigned
        get "/insights", PromoterController, :insights
      end

      scope "/promoter" do
        get "/:id", PromoterController, :promoter_store
      end

      scope "/voucher" do
        pipe_through [:snake_case, :user_authentication, :deprecated]
        get "/event/:event_id", VoucherController, :index
        post "/", VoucherController, :create
        patch "/:id", VoucherController, :update
        delete "/:id", VoucherController, :delete
      end

      scope "/voucher" do
        pipe_through [:snake_case, :deprecated]
        get "/:id", VoucherController, :show
      end

      scope "/vouchers" do
        pipe_through [:snake_case, :verify_header]
        resources "/", VoucherController
      end

      scope "/events" do
        pipe_through :user_authentication
        post "/create", EventController, :create

        post "/:event_id/publish", EventController, :publish
        post "/emails/sample-ticket", EventController, :send_sample_ticket_mail
        get "/promoter/:event_id", EventController, :promoter_details
        get "/edit/:id", EventController, :show_edit
        post "/:event_id/tracking-pixels", TrackingPixelController, :create
        patch "/:event_id/tracking-pixels/:tracking_pixel_id", TrackingPixelController, :update
        delete "/:event_id/tracking-pixels/:tracking_pixel_id", TrackingPixelController, :delete
        post "/:event_id/emails/event", EventController, :send_event_creation_mail
        post "/:id/emails/admission-control", EventController, :send_admission_control_mail
        patch "/:id", EventController, :update
        patch "/:id/approve", EventController, :approve
        patch "/:id/visible", EventController, :visible
        patch "/:id/permissions", EventController, :create_permissions
        patch "/:id/permissions/:permission_id", EventController, :update_permissions
        delete "/:id/permissions/:permission_id", EventController, :delete_permissions
        get "/:event_id/invitations", InvitationController, :index
        get "/:event_id/invitations/count", InvitationController, :count_invitations
        post "/:event_id/invitations", InvitationController, :create
        delete "/invitations/:invitation_id", InvitationController, :delete
        get "/:event_id/invitations/statistics", InvitationController, :statistics
        get "/:event_id/promoter", EventController, :event_promoter
        get "/spotify/artists/:query", SpotifyController, :search
        get "/spotify/v2/search", SpotifyController, :search
        get "/:id/access", EventController, :access

        scope "/:event_id" do
          post "/channel-configs", ChannelConfigController, :actions
          get "/channel-configs", ChannelConfigController, :index
          patch "/channel-configs", ChannelConfigController, :update
          delete "/channel-configs/:channel_id", ChannelConfigController, :delete
        end

        scope "/" do
          pipe_through :deprecated
          get "/:id/users/:user_id/access", EventController, :access
        end
      end

      scope "/event-copy-jobs" do
        pipe_through [:user_authentication, :snake_case]

        resources "/", EventCopyJobController, only: [:index, :show, :create] do
          resources "/executioners", EventCopyJobController, only: [:show], singleton: false, as: "executioner"
        end
      end

      scope "/sales-channels" do
        pipe_through [:snake_case, :verify_header]
        resources "/", SalesChannelController, except: [:new, :edit]
        get "/:token/tickets", SalesChannelController, :get_sales_channel_tickets
      end

      scope "/events" do
        pipe_through :verify_header
        get "/invitations/:invitation_id", InvitationController, :show
        patch "/invitations/:invitation_id", InvitationController, :update
      end

      scope "/events" do
        pipe_through :cache_headers

        scope "/" do
          pipe_through :verify_header
          get "/", EventController, :index
        end

        post "/search", EventController, :search
        get "/:id", EventController, :show
        get "/:id/ics", EventController, :calendar
        get "/:id/promoter-event", EventController, :promoter_event_service
        post "/:id/status", EventController, :promoter_approve_event
        get "/spotify/v2/artists/:id", SpotifyController, :get_artist
        get "/spotify/v2/artists/:id/top-tracks", SpotifyController, :get_artist_top_tracks
        get "/:event_id/channel-configs/:token", ChannelConfigController, :show_for_token
        post "/scripts/:script_id/status", EventController, :execute_script
      end

      scope "/events/:event_id/seatsio" do
        pipe_through :user_authentication

        get "/event", SeatsioController, :get_event

        get "/comment-groups", SeatCommentGroupController, :index
        post "/comment-groups", SeatCommentGroupController, :create
        get "/comment-groups/:group_key", SeatCommentGroupController, :show
        patch "/comment-groups/:id", SeatCommentGroupController, :update
        delete "/comment-groups/:group_key", SeatCommentGroupController, :delete

        post "/manage-objects", SeatsioController, :manage_objects

        get "/get-remaining-forsale-edits", SeatsioController, :get_remaining_forsale_edits

        get "/comment-groups/:group_key/comments",
            SeatCommentController,
            :index

        post "/comment-groups/:group_key/comments",
             SeatCommentController,
             :create

        get "/comments", SeatCommentController, :index
        post "/comments", SeatCommentController, :index
        delete "/comments/:id", SeatCommentController, :delete

        get "/comment-history/:history_id", SeatCommentController, :history
      end

      # this has to be unauthenticated for customers to be able to release objects when removing tickets from cart
      scope "/seatsio" do
        post "/hold-token", SeatsioController, :create_hold_token
        patch "/hold-token/:hold_token", SeatsioController, :update_hold_token_expiry
        post "/release-object/:event_id", SeatsioController, :release_object
      end

      scope "/assets" do
        pipe_through [:snake_case, :user_authentication]

        get "/", AssetController, :get
        post "/events", AssetController, :upload_events
        post "/venues", AssetController, :upload_venues
        post "/seating_plans", AssetController, :upload_seating_plans
      end

      scope "/" do
        pipe_through :user_authentication
        resources "/tracking_links", TrackingLinkController, except: [:new, :edit]
        get "/tracking-links/sync-analytics", TrackingLinkController, :sync_analytics
      end

      scope "/" do
        pipe_through [:user_authentication, :snake_case]

        # Multi Level Pricing
        resources "/mlp", MultiLevelPricingController, except: [:new, :edit]

        # Multi Level Pricing Modifiers
        resources "/mlpm", MultiLevelPricingModifierController, except: [:new, :edit]
      end

      scope "/" do
        pipe_through [:verify_header, :snake_case]
        resources "/entrance-areas", EntranceAreaController, except: [:new, :edit]
        post "/permissions", PermissionController, :validate
      end

      scope "/event-permissions" do
        pipe_through [:user_authentication, :snake_case]

        get "/", PermissionController, :get_permissions
      end

      scope "/availabilities" do
        get "/", AvailabilityController, :index
        get "/:availability_id", AvailabilityController, :show

        scope "/" do
          pipe_through :user_authentication
          post "/", AvailabilityController, :create
          put "/:availability_id", AvailabilityController, :update
          delete "/:availability_id", AvailabilityController, :delete
        end
      end

      scope "/services" do
        scope "/events" do
          get "/invitations/:invitation_id", InvitationController, :show_for_service
        end
      end

      scope "/services", Services do
        pipe_through :snake_case
        get "/events/:event_id/ticket-categories/:id", ServicesTicketCategoryController, :show
        get "/events/:id", EventController, :show
        get "/tracking-pixels", TrackingPixelController, :index
        post "/sellers", SellerController, :create
        get "/sellers/:seller_id", SellerController, :show
        post "/sellers/:seller_id/permissions", SellerController, :check_permission
        get "/sales-channels/:id", SalesChannelController, :show
      end

      scope "/events" do
        pipe_through :user_authentication
        resources "/:event_id/fees", FeeController, except: [:new, :edit]
        resources "/:event_id/extras", ExtraController, except: [:new, :edit]
      end

      scope "/ticket-categories" do
        pipe_through [:snake_case, :user_authentication]
        resources "/", TicketCategoryController, except: [:new, :edit]
      end

      scope "/variants" do
        pipe_through [:snake_case, :verify_header]
        resources "/", VariantController, except: [:new, :edit]
      end

      scope "/sales-permissions" do
        pipe_through [:snake_case, :user_authentication]
        post "/", SalesPermissionController, :create
        get "/", SalesPermissionController, :index
        delete "/:id", SalesPermissionController, :delete
      end

      scope "/seller-permissions" do
        pipe_through [:snake_case, :user_authentication]
        resources "/", SellerPermissionController, except: [:new, :edit]
      end

      scope "/sellers" do
        pipe_through [:snake_case, :user_authentication]
        get "/", SellerController, :index
      end
    end
  end

  scope "/api", EventsServiceWeb do
    pipe_through :api
    get "/location", LocationController, :index
    resources "/trackingLinks", TrackingLinkController, except: [:new, :edit]
    get "/tracking-links/sync-analytics", TrackingLinkController, :sync_analytics
  end

  scope "/api/voucher", EventsServiceWeb do
    pipe_through [:api, :snake_case, :user_authentication, :deprecated]
    get "/event/:event_id", VoucherController, :index
    post "/", VoucherController, :create
    patch "/:id", VoucherController, :update
    delete "/:id", VoucherController, :delete
  end

  scope "/api/voucher", EventsServiceWeb do
    pipe_through [:api, :snake_case, :deprecated]
    get "/:id", VoucherController, :show
  end

  scope "/api/promoter", EventsServiceWeb do
    pipe_through [:api, :user_authentication]
    post "/", PromoterController, :create
    get "/onboarding/resume", PromoterController, :resume
    get "/legal-entity", PromoterController, :get_legal_entity
    get "/validate-legal-entity", PromoterController, :validate_legal_entity
    get "/events", PromoterController, :events
    get "/userAssigned", PromoterController, :user_assigned
    get "/insights", PromoterController, :insights
  end

  scope "/api/promoter", EventsServiceWeb do
    pipe_through :api
    get "/:id", PromoterController, :promoter_store
  end

  scope "/api/venues", EventsServiceWeb do
    pipe_through :api
    get "/:id", VenueController, :show
    post "/search", VenueController, :search

    scope "/" do
      pipe_through :user_authentication
      post "/create", VenueController, :create
    end
  end

  defp authentication_dispatcher(conn, _opts) do
    opts = AuthenticationDispatcher.init(client: RBACClient.client_config())

    AuthenticationDispatcher.call(conn, opts)
  end

  defp authenticate(conn, _opts) do
    opts = Authenticate.init(client: RBACClient.client_config())

    Authenticate.call(conn, opts)
  end
end
