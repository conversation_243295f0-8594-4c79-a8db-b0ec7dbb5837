defmodule EventsServiceWeb.Services.SalesChannelController do
  @moduledoc false

  use EventsServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs
  use Params

  alias EventsService.Events.SalesChannel
  alias EventsServiceWeb.ApiSchemas.Services.SalesChannelSchema.SalesChannel, as: SalesChannelResponse
  alias EventsServiceWeb.ChangesetJSON
  alias OpenApiSpex.Reference

  require Logger

  action_fallback EventsServiceWeb.FallbackController

  tags ["Sales Channels"]

  operation :show,
    summary: "Get Sales Channel by ID",
    parameters: [
      id: [
        in: :path,
        description: "Sales Channel ID",
        type: :string,
        example: "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
      ]
    ],
    responses: %{
      :ok => {"Sales Channel response", "application/json", SalesChannelResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"}
    }

  defparams(
    show_params(%{
      id!: Ecto.UUID
    })
  )

  def show(conn, %{"id" => id} = params) do
    with {_, true} <- {:auth, service_authorized?(conn)},
         {_, %{valid?: true}} <- {:params, show_params(params)},
         {_, {:ok, sales_channel}} <- {:sales_channel, SalesChannel.get_sales_channel(id, [:channel_config])} do
      Logger.debug("Get sales channel with id: #{inspect(id)}")

      render(conn, :show, sales_channel: sales_channel)
    else
      {:auth, false} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{
          error_code: :unauthorized,
          message: "Service unauthorized."
        })

      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:sales_channel, _} ->
        Logger.error("Can't show Sales Channel for id #{inspect(id)} because it doesn't exist.")

        conn
        |> put_status(:not_found)
        |> json(%{error_code: :not_found, message: "Sales Channel not found"})
    end
  end
end
