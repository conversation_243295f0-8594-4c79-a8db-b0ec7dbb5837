defmodule EventsServiceWeb.Services.ChannelConfigJSON do
  alias EventsService.Channels.ChannelConfig

  @doc """
  Renders a list of channel_configs.
  """
  def index(%{channel_configs: channel_configs}) do
    for(channel_config <- channel_configs, do: data(channel_config))
  end

  @doc """
  Renders a single channel_config.
  """
  def show(%{channel_config: channel_config}) do
    data(channel_config)
  end

  def data(%ChannelConfig{} = channel_config) do
    %{
      id: channel_config.id,
      token: channel_config.token,
      channelKey: channel_config.channel_key,
      type: channel_config.type,
      value: channel_config.value,
      label: channel_config.label,
      description: channel_config.description,
      color: channel_config.color,
      amountOfObjects: channel_config.amount_of_objects,
      validUntil: channel_config.valid_until,
      eventId: channel_config.event_id
    }
  end

  def data(_), do: nil
end
