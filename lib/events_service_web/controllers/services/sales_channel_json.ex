defmodule EventsServiceWeb.Services.SalesChannelJSON do
  @moduledoc false

  alias EventsService.Events.SalesChannel
  alias EventsServiceWeb.Services.ChannelConfigJSON

  @doc """
  Renders a single sales channel.
  """
  def show(%{sales_channel: %SalesChannel{} = sales_channel}) do
    data(sales_channel)
  end

  defp data(%SalesChannel{} = sales_channel) do
    # styler:sort
    %{
      channelConfig: ChannelConfigJSON.data(sales_channel.channel_config),
      channelConfigId: sales_channel.channel_config_id,
      id: sales_channel.id,
      insertedAt: sales_channel.inserted_at,
      originalPrice: sales_channel.original_price,
      quotaMode: sales_channel.quota_mode,
      updatedAt: sales_channel.updated_at,
      variantId: sales_channel.variant_id
    }
  end
end
