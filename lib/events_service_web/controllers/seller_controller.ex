defmodule EventsServiceWeb.SellerController do
  @moduledoc false

  use EventsServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs
  use Params

  alias EventsService.Sellers
  alias EventsService.Vendor
  alias EventsService.Vendor.Promoter
  alias EventsServiceWeb.ApiSchemas.SellerSchema
  alias EventsServiceWeb.ChangesetJSON
  alias EventsServiceWeb.Plugs.Authorize
  alias EventsServiceWeb.SellerJSON
  alias OpenApiSpex.Reference

  require Logger

  action_fallback EventsServiceWeb.FallbackController

  #plug Authorize, [rule: ["internal", "seller", "write"]] when action in [:create]

  tags ["Sellers"]

  operation :index,
    security: [%{"bearerAuth" => []}],
    summary: "Get sellers available for the current user",
    parameters: [
      page: [in: :query, type: :integer, description: "Page number (1-based)"],
      pageSize: [in: :query, type: :integer, description: "Number of items per page"]
    ],
    responses: %{
      :ok =>
        {"‼️ Response schema/format can differ based on the seller type", "application/json",
         SellerSchema.SellersResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"}
    }

  defparams(
    index_params(%{
      page: [field: :integer, default: 1],
      page_size: [field: :integer, default: 10]
    })
  )

  def index(%{assigns: %{current_user_id: user_id}} = conn, params) do
    case {:params, index_params(params)} do
      {_, %{valid?: true, changes: params} = _changeset} ->
        page = Sellers.paginate_sellers_for_user(user_id, params)

        conn
        |> put_status(:ok)
        |> put_view(SellerJSON)
        |> render(:index, page: page)

      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)
    end
  end

  operation :create,
    security: [%{"bearerAuth" => []}],
    summary: "Create a new seller",
    request_body: {"Seller creation request", "application/json", SellerSchema.CreateSellerRequest},
    responses: %{
      :created => {"Seller created", "application/json", SellerSchema.Seller},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  defparams(
    create_params(%{
      type!: [field: Ecto.Enum, values: [:ORGANIZER]],
      promoter_id!: Ecto.UUID,
      display_name: :string
    })
  )

  def create(conn, params) do
    with {_, %{valid?: true, changes: validated_params} = _changeset} <-
           {:params, create_params(params)},
         {_, %Promoter{} = promoter} <-
           {:promoter, Vendor.get_promoter_by_id(validated_params[:promoter_id])},
         {_, {:ok, seller}} <-
           {:create_seller, Sellers.create_organizer_seller(promoter, validated_params)} do
      conn
      |> put_status(:created)
      |> put_view(SellerJSON)
      |> render(:show, seller: seller)
    else
      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:promoter, nil} ->
        conn
        |> put_status(:bad_request)
        |> json(%{message: "Promoter not found", error_code: :promoter_not_found})

      {:create_seller, {:error, _failed_operation, failed_value}} ->
        Logger.error("Failed to create seller: #{inspect(failed_value)}")

        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: failed_value)

      {:create_seller, {:error, error}} ->
        Logger.error("Failed to create seller: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{message: "Failed to create seller", error_code: :internal_server_error})
    end
  end
end
