defmodule ExServiceClient.Services.OrdersService do
  @moduledoc false
  alias ExServiceClient.Services.OrdersService.CartRequests
  alias ExServiceClient.Services.OrdersService.CounterRequests
  alias ExServiceClient.Services.OrdersService.EventsRequests
  alias ExServiceClient.Services.OrdersService.OrderRequests
  alias ExServiceClient.Services.OrdersService.TicketRequests

  @spec get_all_sold_and_pending_by_event_id(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_all_sold_and_pending_by_event_id(code), to: TicketRequests

  @spec get_all_sold_and_pending_by_variant_id(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_all_sold_and_pending_by_variant_id(code), to: TicketRequests

  @spec get_by_invitation_id(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_by_invitation_id(code), to: TicketRequests

  @spec get_popular_event_ids() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_popular_event_ids(), to: EventsRequests

  @spec get_cart_total(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_cart_total(code), to: CartRequests

  @spec count_sold_and_pending_by_event_id(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate count_sold_and_pending_by_event_id(code), to: TicketRequests

  @spec count_sold_and_pending_by_category_id(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate count_sold_and_pending_by_category_id(code), to: TicketRequests

  @spec count_sold_and_pending_by_variant_id(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate count_sold_and_pending_by_variant_id(code), to: TicketRequests

  @spec count_sold_and_pending_by_voucher_id(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate count_sold_and_pending_by_voucher_id(code), to: TicketRequests

  @spec calculate_total_sales_by_event_ids(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate calculate_total_sales_by_event_ids(params), to: TicketRequests

  @spec count_sold_and_pending_variants_by_variant_ids(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate count_sold_and_pending_variants_by_variant_ids(code), to: TicketRequests

  @spec count_sold_and_pending_vouchers_by_voucher_ids(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate count_sold_and_pending_vouchers_by_voucher_ids(code), to: TicketRequests

  @spec calculate_total_checked_in_by_event_ids(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate calculate_total_checked_in_by_event_ids(params), to: TicketRequests

  @spec resend_ticket_emails(any()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate resend_ticket_emails(params), to: TicketRequests

  @spec create_order(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate create_order(params), to: OrderRequests

  @spec update_order(any, any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate update_order(order_id, params), to: OrderRequests

  @spec trigger_ticket_counter_by_event_id(Ecto.UUID.t()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate trigger_ticket_counter_by_event_id(event_id), to: CounterRequests

  @spec trigger_ticket_counter_by_variant_id(Ecto.UUID.t()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate trigger_ticket_counter_by_variant_id(variant_id), to: CounterRequests

  @spec trigger_ticket_counter_by_voucher_id(Ecto.UUID.t()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate trigger_ticket_counter_by_voucher_id(voucher_id), to: CounterRequests

  @spec trigger_ticket_counter_by_ticket_category_id(Ecto.UUID.t()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate trigger_ticket_counter_by_ticket_category_id(ticket_category_id), to: CounterRequests
end
