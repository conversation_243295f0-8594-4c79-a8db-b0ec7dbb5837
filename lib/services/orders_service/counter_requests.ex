defmodule ExServiceClient.Services.OrdersService.CounterRequests do
  @moduledoc """
  Collection of all counter requests for the orders-service.
  """

  use ExServiceClient.Clients.BaseClient

  def trigger_ticket_counter_by_event_id(event_id) do
    [endpoint: :orders]
    |> client()
    |> Tesla.get("/counters/events/#{event_id}")
    |> parse_response()
  end

  def trigger_ticket_counter_by_variant_id(variant_id) do
    [endpoint: :orders]
    |> client()
    |> Tesla.get("/counters/variants/#{variant_id}")
    |> parse_response()
  end

  def trigger_ticket_counter_by_voucher_id(voucher_id) do
    [endpoint: :orders]
    |> client()
    |> Tesla.get("/counters/vouchers/#{voucher_id}")
    |> parse_response()
  end

  def trigger_ticket_counter_by_ticket_category_id(ticket_category_id) do
    [endpoint: :orders]
    |> client()
    |> Tesla.get("/counters/ticket_categories/#{ticket_category_id}")
    |> parse_response()
  end
end
