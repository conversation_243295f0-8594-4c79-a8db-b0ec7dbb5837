defmodule ExServiceClient.Services.EventsService.SalesChannel do
  @moduledoc """
  A module representing a Sales Channel entity and providing functions to interact with sales channel-related endpoints.

  ## Associated Resources

  A Sales Channel includes the following associated resources:
  - **Channel Config**: The channel configuration associated with this sales channel

  ## Usage Examples

  ```elixir
  # Fetch a sales channel by ID
  {:ok, sales_channel} = ExServiceClient.Services.EventsService.SalesChannel.get("c0776c17-19ea-4afa-ba15-fba6769fcf71")
  ```

  ## Caching

  All get operations are cached using the configured cache store. The cache key is generated based on
  the module name and the sales channel ID.
  """

  use ExServiceClient.Cacheable
  use ExServiceClient.Clients.BaseClient

  alias ExServiceClient.Services.EventsService.ChannelConfig
  alias ExServiceClient.Services.EventsService.SalesChannel
  alias ExServiceClient.Util.DateTimeUtil

  @opts [endpoint: :events]

  defstruct [
    :id,
    :originalPrice,
    :quotaMode,
    :variantId,
    :channelConfig,
    :channelConfigId,
    :insertedAt,
    :updatedAt
  ]

  @type t :: %__MODULE__{
          id: binary(),
          originalPrice: integer(),
          quotaMode: String.t(),
          variantId: binary(),
          channelConfig: ChannelConfig.t() | nil,
          channelConfigId: binary(),
          insertedAt: DateTime.t(),
          updatedAt: DateTime.t()
        }

  defimpl Poison.Decoder, for: SalesChannel do
    def decode(%SalesChannel{} = sales_channel, _options) do
      %SalesChannel{
        insertedAt: inserted_at,
        updatedAt: updated_at
      } = sales_channel

      %{
        sales_channel
        | insertedAt: DateTimeUtil.parse_iso8601(inserted_at),
          updatedAt: DateTimeUtil.parse_iso8601(updated_at)
      }
    end
  end

  @decorate cacheable(
              cache: Cache,
              key: cache_key(__MODULE__, id),
              opts: [ttl: Cache.get_ttl()]
            )
  @spec get(binary()) :: {:ok, t()} | {:error, map()}
  def get(id) do
    @opts
    |> client()
    |> Tesla.get("/services/sales-channels/#{id}")
    |> parse_response(&decode!/1)
  end

  defp decode!(data) do
    Poison.decode!(data,
      as: %SalesChannel{
        channelConfig: %ChannelConfig{}
      }
    )
  end
end
