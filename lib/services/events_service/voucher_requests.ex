defmodule ExServiceClient.Services.EventsService.VoucherRequests do
  @moduledoc """
  """
  use ExServiceClient.Clients.BaseClient

  @prefix "voucher"

  defstruct [
    :active,
    :code,
    :description,
    # deprecated: use scopeId
    :eventId,
    :firestore_id,
    :id,
    :limitPerRedemption,
    :minItems,
    :quota,
    :scopeId,
    :sopeType,
    :type,
    :used,
    :value
  ]

  # styler:sort
  @type t ::
          %__MODULE__{
            # Add fields here if needed
            active: :boolean,
            code: String.t(),
            description: String.t() | nil,
            # deprecated: use scopeId
            eventId: String.t() | nil,
            firestore_id: String.t() | nil,
            id: Ecto.UUID.t(),
            limitPerRedemption: integer() | nil,
            minItems: integer() | nil,
            quota: integer() | nil,
            scopeId: String.t(),
            sopeType: String.t(),
            type: String.t(),
            used: integer(),
            value: integer()
          }

  # deprecated
  def get_voucher_by_code(nil), do: {:ok, nil}

  def get_voucher_by_code(code) do
    [endpoint: :events]
    |> client()
    |> Tesla.get("/#{@prefix}/#{code}")
    |> parse_response()
  end

  def get_vouchers_by_code(code) do
    [endpoint: :events]
    |> client()
    |> Tesla.get("/vouchers?code=#{code}&scope=service")
    |> parse_response()
  end

  def use_voucher_by_code(code) do
    [endpoint: :events]
    |> client()
    |> Tesla.post("/#{@prefix}/#{code}", %{"action" => "use"})
    |> parse_response()
  end
end
