defmodule ExServiceClient.Services.EventsService.Variant do
  @moduledoc """
  Represents and includes basic operations for event ticket variants in the Events Service.

  ## Usage

  ```elixir
  # Fetch a variant by ID
  {:ok, variant} = ExServiceClient.Services.EventsService.Variant.get("c0776c17-19ea-4afa-ba15-fba6769fcf71")
  ```

  The module implements caching functionality and uses the Events Service API endpoint
  for data retrieval. All datetime values are automatically parsed from ISO8601 format.
  """

  use ExServiceClient.Cacheable
  use ExServiceClient.Clients.BaseClient

  alias ExServiceClient.Services.EventsService.Availability
  alias ExServiceClient.Services.EventsService.MultiLevelPricingModifier
  alias ExServiceClient.Services.EventsService.SalesChannel
  alias ExServiceClient.Services.EventsService.TicketCategory
  alias ExServiceClient.Services.EventsService.Variant
  alias ExServiceClient.Util.DateTimeUtil

  @opts [endpoint: :events]

  defstruct [
    :id,
    :eventId,
    :unitPrice,
    :quota,
    :visibilityBeforeSalesStarted,
    :visibilityAfterSalesEnded,
    :predecessorId,
    :isSoldOutFromReservation,
    :presaleStartDate,
    :orderNo,
    :status,
    :successors,
    :ticketCategory,
    :availability,
    :distributionType,
    :salesChannel,
    :minAmount,
    :maxAmount,
    :multiLevelPricingModifier,
    :insertedAt
  ]

  @type t :: %__MODULE__{
          id: binary(),
          eventId: binary(),
          unitPrice: integer(),
          quota: integer(),
          visibilityBeforeSalesStarted: String.t(),
          visibilityAfterSalesEnded: String.t(),
          predecessorId: binary() | nil,
          isSoldOutFromReservation: boolean(),
          presaleStartDate: DateTime.t() | nil,
          orderNo: integer(),
          status: String.t(),
          successors: [binary()],
          ticketCategory: TicketCategory.t(),
          availability: Availability.t(),
          distributionType: String.t(),
          salesChannel: SalesChannel.t() | nil,
          minAmount: integer(),
          maxAmount: integer(),
          multiLevelPricingModifier: MultiLevelPricingModifier.t(),
          insertedAt: DateTime.t()
        }

  defimpl Poison.Decoder, for: Variant do
    @spec decode(Variant.t(), any()) :: Variant.t()
    def decode(%Variant{presaleStartDate: presale_start_date} = variant, _options) do
      presale_start_date = DateTimeUtil.parse_iso8601(presale_start_date)

      %{variant | presaleStartDate: presale_start_date}
    end
  end

  @decorate cacheable(
              cache: Cache,
              key: cache_key(__MODULE__, id),
              opts: [ttl: Cache.get_ttl()]
            )
  @spec get(binary()) :: {:ok, t()} | {:error, map()}
  def get(id) do
    @opts
    |> client()
    |> Tesla.get("/variants/#{id}", query: [scope: "service"])
    |> parse_response(&decode!/1)
  end

  defp decode!(data) do
    Poison.decode!(data,
      as: %Variant{
        availability: %Availability{},
        ticketCategory: %TicketCategory{},
        multiLevelPricingModifier: %MultiLevelPricingModifier{}
      }
    )
  end
end
