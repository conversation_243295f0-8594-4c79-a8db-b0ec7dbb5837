defmodule ExServiceClient.Services.PdfService.PdfRequests do
  @moduledoc """
  Collection of all PDF requests for the pdf-service.
  """

  use ExServiceClient.Clients.BaseClient

  def get_hard_tickets_pdf(params) do
    [endpoint: :pdf]
    |> client()
    |> Tesla.post("/hard-tickets", params)
    |> parse_response()
  end

  def get_order_summary_pdf(params) do
    [endpoint: :pdf]
    |> client()
    |> Tesla.post("/order-summary", params)
    |> parse_response()
  end

  def get_event_summary_pdf(params) do
    [endpoint: :pdf]
    |> client()
    |> Tesla.post("/event-summary", params)
    |> parse_response()
  end
end
